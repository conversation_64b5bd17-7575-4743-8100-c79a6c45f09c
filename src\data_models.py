"""
Data models for the Wiz Aroma Food Delivery system.
Contains classes and data structures for orders, users, and other entities.
"""

from telebot.handler_backends import State, StatesGroup
from typing import Dict, List, Optional, Union, Any
import datetime

# Dictionary data storage for in-memory data
user_points = {}  # Stores user points: {user_id: points}
user_order_history = {}  # Stores user order history: {user_id: [order1, order2, ...]}
user_names = {}  # Stores user names: {user_id: name}
user_phone_numbers = {}  # Stores user phone numbers: {user_id: phone_number}
user_emails = {}  # Stores user email addresses: {user_id: email}
orders = {}  # Active orders being created: {user_id: order_data}
order_status = {}  # Status of orders in progress: {user_id: status}
pending_admin_reviews = (
    {}
)  # Orders waiting for admin review: {order_number: order_data}
admin_remarks = {}  # Admin remarks on orders: {order_number: remarks}
awaiting_receipt = {}  # Orders waiting for payment receipt: {order_number: order_data}
delivery_locations = {}  # Temporary storage for delivery locations: {user_id: location}
current_order_numbers = {}  # Current order numbers: {user_id: order_number}
user_order_counts = {}  # Count of orders per user: {user_id: count}
favorite_orders = {}  # Favorite orders: {user_id: [order1, order2, ...]}

# Delivery Personnel Management Data
delivery_personnel = {}  # Delivery personnel: {personnel_id: personnel_data}
delivery_personnel_assignments = {}  # Active assignments: {assignment_id: assignment_data}
delivery_personnel_availability = {}  # Availability status: {personnel_id: status}
delivery_personnel_capacity = {}  # Current capacity: {personnel_id: active_order_count}
delivery_personnel_zones = {}  # Zone assignments: {personnel_id: [zone_ids]}
delivery_personnel_performance = {}  # Performance metrics: {personnel_id: metrics}
delivery_personnel_earnings = {}  # Earnings tracking: {personnel_id: earnings_data}

# Data loaded from JSON files (initialized in data_storage.py)
areas_data = {"areas": []}
restaurants_data = {"restaurants": []}
menus_data = {"default_menu_items": [], "restaurant_menus": {}}
delivery_locations_data = {"delivery_locations": []}
delivery_fees_data = {"delivery_fees": []}


class OrderStates(StatesGroup):
    """States for the order flow"""

    SELECTING_ITEMS = State()
    AWAITING_ORDER_DESCRIPTION = State()
    AWAITING_DELIVERY_LOCATION = State()
    AWAITING_DELIVERY_NAME = State()
    AWAITING_PHONE_NUMBER = State()
    AWAITING_CONFIRMATION = State()
    AWAITING_ADMIN_REVIEW = State()
    AWAITING_POINT_DECISION = State()
    AWAITING_POINTS_CONFIRMATION = State()
    AWAITING_PAYMENT_METHOD = State()
    AWAITING_RECEIPT = State()
    AWAITING_VERIFICATION = State()
    AWAITING_FINAL_CONFIRMATION = State()


class Order:
    """Order class for tracking order details"""

    def __init__(self, user_id: int, chat_id: int, restaurant_id: int):
        self.user_id = user_id
        self.chat_id = chat_id
        self.restaurant_id = restaurant_id
        self.items: List[Dict[str, Any]] = []
        self.delivery_name: str = ""
        self.delivery_location: str = ""
        self.phone_number: str = ""
        self.order_number: str = ""
        self.created_at = datetime.datetime.now()
        self.special_instructions: str = ""
        self.payment_method: str = ""
        self.paid: bool = False
        self.subtotal: float = 0
        self.delivery_fee: float = 0
        self.points_used: int = 0
        # New fields for tracking cash vs point payment breakdown
        self.cash_amount: float = 0  # Total cash paid (subtotal + delivery_fee_cash)
        self.points_amount: int = 0  # Total points used (same as points_used for backward compatibility)
        self.delivery_fee_cash: float = 0  # Portion of delivery fee paid in cash
        self.delivery_fee_points: int = 0  # Portion of delivery fee paid in points

    def add_item(self, item: Dict[str, Any]) -> None:
        """Add an item to the order"""
        self.items.append(item)
        self.subtotal += item.get("price", 0)

    def remove_item(self, index: int) -> bool:
        """Remove an item from the order by index"""
        if 0 <= index < len(self.items):
            item = self.items.pop(index)
            self.subtotal -= item.get("price", 0)
            return True
        return False

    def clear_items(self) -> None:
        """Clear all items from the order"""
        self.items = []
        self.subtotal = 0

    def set_delivery_location(self, location: str, fee: float) -> None:
        """Set the delivery location and fee"""
        self.delivery_location = location
        self.delivery_fee = fee

    def set_delivery_name(self, name: str) -> None:
        """Set the delivery name"""
        self.delivery_name = name

    def set_phone_number(self, phone: str) -> None:
        """Set the phone number"""
        self.phone_number = phone

    def set_special_instructions(self, instructions: str) -> None:
        """Set special instructions"""
        self.special_instructions = instructions

    def set_payment_method(self, method: str) -> None:
        """Set the payment method"""
        self.payment_method = method

    def mark_as_paid(self) -> None:
        """Mark the order as paid"""
        self.paid = True

    def use_points(self, points: int) -> None:
        """Use points for the order"""
        self.points_used = points
        self.points_amount = points
        # Assume points are used for delivery fee first
        if points <= self.delivery_fee:
            self.delivery_fee_points = points
            self.delivery_fee_cash = self.delivery_fee - points
        else:
            self.delivery_fee_points = int(self.delivery_fee)
            self.delivery_fee_cash = 0
        # Calculate cash amount (subtotal + remaining delivery fee)
        self.cash_amount = self.subtotal + self.delivery_fee_cash

    def calculate_payment_breakdown(self) -> None:
        """Calculate payment breakdown for existing orders (backward compatibility)"""
        if self.points_used > 0:
            # Points were used for delivery fee
            self.points_amount = self.points_used
            self.delivery_fee_points = min(self.points_used, int(self.delivery_fee))
            self.delivery_fee_cash = self.delivery_fee - self.delivery_fee_points
            self.cash_amount = self.subtotal + self.delivery_fee_cash
        else:
            # No points used, all cash payment
            self.points_amount = 0
            self.delivery_fee_points = 0
            self.delivery_fee_cash = self.delivery_fee
            self.cash_amount = self.subtotal + self.delivery_fee

    def to_dict(self) -> Dict[str, Any]:
        """Convert order to dictionary for storage"""
        return {
            "user_id": self.user_id,
            "chat_id": self.chat_id,
            "restaurant_id": self.restaurant_id,
            "items": self.items,
            "delivery_name": self.delivery_name,
            "delivery_location": self.delivery_location,
            "phone_number": self.phone_number,
            "order_number": self.order_number,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "special_instructions": self.special_instructions,
            "payment_method": self.payment_method,
            "paid": self.paid,
            "subtotal": self.subtotal,
            "delivery_fee": self.delivery_fee,
            "points_used": self.points_used,
            # New payment breakdown fields
            "cash_amount": self.cash_amount,
            "points_amount": self.points_amount,
            "delivery_fee_cash": self.delivery_fee_cash,
            "delivery_fee_points": self.delivery_fee_points,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Order":
        """Create an order from a dictionary"""
        order = cls(
            user_id=data.get("user_id", 0),
            chat_id=data.get("chat_id", 0),
            restaurant_id=data.get("restaurant_id", 0),
        )
        order.items = data.get("items", [])
        order.delivery_name = data.get("delivery_name", "")
        order.delivery_location = data.get("delivery_location", "")
        order.phone_number = data.get("phone_number", "")
        order.order_number = data.get("order_number", "")
        order.special_instructions = data.get("special_instructions", "")
        order.payment_method = data.get("payment_method", "")
        order.paid = data.get("paid", False)
        order.subtotal = data.get("subtotal", 0)
        order.delivery_fee = data.get("delivery_fee", 0)
        order.points_used = data.get("points_used", 0)
        # New payment breakdown fields with backward compatibility
        order.cash_amount = data.get("cash_amount", order.subtotal + order.delivery_fee)
        order.points_amount = data.get("points_amount", order.points_used)
        order.delivery_fee_cash = data.get("delivery_fee_cash", order.delivery_fee if order.points_used == 0 else max(0, order.delivery_fee - order.points_used))
        order.delivery_fee_points = data.get("delivery_fee_points", min(order.points_used, order.delivery_fee) if order.points_used > 0 else 0)

        # Parse created_at if it exists
        created_at = data.get("created_at")
        if created_at:
            try:
                order.created_at = datetime.datetime.strptime(
                    created_at, "%Y-%m-%d %H:%M:%S"
                )
            except ValueError:
                order.created_at = datetime.datetime.now()

        return order


class DeliveryPersonnel:
    """Represents a delivery personnel in the system"""

    def __init__(self, personnel_id: str):
        self.personnel_id = personnel_id
        self.name: str = ""
        self.phone_number: str = ""
        self.telegram_id: Optional[str] = None
        self.email: Optional[str] = None
        self.service_areas: List[str] = []  # List of area IDs they can serve
        self.max_capacity: int = 5  # Maximum concurrent orders
        self.current_capacity: int = 0  # Current active orders
        self.status: str = "offline"  # available, busy, offline
        self.created_at = datetime.datetime.now()
        self.last_active = datetime.datetime.now()
        self.is_verified: bool = False
        self.emergency_contact: Optional[str] = None
        self.vehicle_type: str = "motorcycle"  # motorcycle, bicycle, car, walking
        self.rating: float = 5.0
        self.total_deliveries: int = 0
        self.successful_deliveries: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert delivery personnel to dictionary for storage"""
        return {
            "personnel_id": self.personnel_id,
            "name": self.name,
            "phone_number": self.phone_number,
            "telegram_id": self.telegram_id,
            "email": self.email,
            "service_areas": self.service_areas,
            "max_capacity": self.max_capacity,
            "current_capacity": self.current_capacity,
            "status": self.status,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "last_active": self.last_active.strftime("%Y-%m-%d %H:%M:%S"),
            "is_verified": self.is_verified,
            "emergency_contact": self.emergency_contact,
            "vehicle_type": self.vehicle_type,
            "rating": self.rating,
            "total_deliveries": self.total_deliveries,
            "successful_deliveries": self.successful_deliveries,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DeliveryPersonnel":
        """Create delivery personnel from dictionary"""
        personnel = cls(data.get("personnel_id", ""))
        personnel.name = data.get("name", "")
        personnel.phone_number = data.get("phone_number", "")
        personnel.telegram_id = data.get("telegram_id")
        personnel.email = data.get("email")
        personnel.service_areas = data.get("service_areas", [])
        personnel.max_capacity = data.get("max_capacity", 5)
        personnel.current_capacity = data.get("current_capacity", 0)
        personnel.status = data.get("status", "offline")
        personnel.is_verified = data.get("is_verified", False)
        personnel.emergency_contact = data.get("emergency_contact")
        personnel.vehicle_type = data.get("vehicle_type", "motorcycle")
        personnel.rating = data.get("rating", 5.0)
        personnel.total_deliveries = data.get("total_deliveries", 0)
        personnel.successful_deliveries = data.get("successful_deliveries", 0)

        # Parse timestamps
        created_at = data.get("created_at")
        if created_at:
            try:
                personnel.created_at = datetime.datetime.strptime(
                    created_at, "%Y-%m-%d %H:%M:%S"
                )
            except ValueError:
                personnel.created_at = datetime.datetime.now()

        last_active = data.get("last_active")
        if last_active:
            try:
                personnel.last_active = datetime.datetime.strptime(
                    last_active, "%Y-%m-%d %H:%M:%S"
                )
            except ValueError:
                personnel.last_active = datetime.datetime.now()

        return personnel

    def is_available(self) -> bool:
        """Check if personnel is available for new orders"""
        # Use real-time capacity instead of cached current_capacity
        from src.utils.delivery_personnel_utils import get_real_time_capacity
        try:
            real_time_capacity = get_real_time_capacity(self.personnel_id)
            max_allowed_capacity = min(self.max_capacity, 5)  # Enforce global 5-order limit

            return (
                self.status == "available"
                and self.is_verified
                and real_time_capacity < max_allowed_capacity
            )
        except Exception:
            # Fallback to cached capacity if real-time check fails
            max_allowed_capacity = min(self.max_capacity, 5)
            return (
                self.status == "available"
                and self.is_verified
                and self.current_capacity < max_allowed_capacity
            )

    def can_serve_area(self, area_id: str) -> bool:
        """Check if personnel can serve a specific area"""
        return area_id in self.service_areas

    def update_capacity(self, change: int) -> None:
        """Update current capacity (positive to add, negative to remove)"""
        self.current_capacity = max(0, min(self.max_capacity, self.current_capacity + change))
        if self.current_capacity >= self.max_capacity:
            self.status = "busy"
        elif self.current_capacity == 0 and self.status == "busy":
            self.status = "available"

    def get_success_rate(self) -> float:
        """Calculate delivery success rate"""
        if self.total_deliveries == 0:
            return 1.0
        return self.successful_deliveries / self.total_deliveries


class DeliveryAssignment:
    """Represents an order assignment to delivery personnel"""

    def __init__(self, assignment_id: str, order_number: str, personnel_id: str):
        self.assignment_id = assignment_id
        self.order_number = order_number
        self.personnel_id = personnel_id
        self.assigned_at = datetime.datetime.now()
        self.status: str = "assigned"  # assigned, accepted, picked_up, delivered, cancelled
        self.estimated_pickup_time: Optional[datetime.datetime] = None
        self.estimated_delivery_time: Optional[datetime.datetime] = None
        self.actual_pickup_time: Optional[datetime.datetime] = None
        self.actual_delivery_time: Optional[datetime.datetime] = None
        self.delivery_notes: str = ""
        self.customer_rating: Optional[int] = None
        self.delivery_fee: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert assignment to dictionary for storage"""
        return {
            "assignment_id": self.assignment_id,
            "order_number": self.order_number,
            "personnel_id": self.personnel_id,
            "assigned_at": self.assigned_at.strftime("%Y-%m-%d %H:%M:%S"),
            "status": self.status,
            "estimated_pickup_time": self.estimated_pickup_time.strftime("%Y-%m-%d %H:%M:%S") if self.estimated_pickup_time else None,
            "estimated_delivery_time": self.estimated_delivery_time.strftime("%Y-%m-%d %H:%M:%S") if self.estimated_delivery_time else None,
            "actual_pickup_time": self.actual_pickup_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_pickup_time else None,
            "actual_delivery_time": self.actual_delivery_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_delivery_time else None,
            "delivery_notes": self.delivery_notes,
            "customer_rating": self.customer_rating,
            "delivery_fee": self.delivery_fee,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DeliveryAssignment":
        """Create assignment from dictionary"""
        assignment = cls(
            data.get("assignment_id", ""),
            data.get("order_number", ""),
            data.get("personnel_id", "")
        )
        assignment.status = data.get("status", "assigned")
        assignment.delivery_notes = data.get("delivery_notes", "")
        assignment.customer_rating = data.get("customer_rating")
        assignment.delivery_fee = data.get("delivery_fee", 0.0)

        # Parse timestamps
        for field, attr in [
            ("assigned_at", "assigned_at"),
            ("estimated_pickup_time", "estimated_pickup_time"),
            ("estimated_delivery_time", "estimated_delivery_time"),
            ("actual_pickup_time", "actual_pickup_time"),
            ("actual_delivery_time", "actual_delivery_time"),
        ]:
            timestamp = data.get(field)
            if timestamp:
                try:
                    setattr(assignment, attr, datetime.datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S"))
                except ValueError:
                    if field == "assigned_at":
                        assignment.assigned_at = datetime.datetime.now()

        return assignment


class DeliveryPersonnelEarnings:
    """Represents earnings tracking for delivery personnel"""

    def __init__(self, personnel_id: str):
        self.personnel_id = personnel_id
        self.daily_earnings: float = 0.0
        self.weekly_earnings: float = 0.0
        self.current_week_start: datetime.datetime = self._get_week_start()
        self.last_updated: datetime.datetime = datetime.datetime.now()
        self.total_lifetime_earnings: float = 0.0
        self.completed_deliveries_today: int = 0
        self.completed_deliveries_week: int = 0

    def _get_week_start(self) -> datetime.datetime:
        """Get the start of the current week (Monday)"""
        today = datetime.datetime.now()
        # Monday is 0, Sunday is 6
        days_since_monday = today.weekday()
        week_start = today - datetime.timedelta(days=days_since_monday)
        # Set to beginning of day
        return week_start.replace(hour=0, minute=0, second=0, microsecond=0)

    def _get_day_start(self) -> datetime.datetime:
        """Get the start of the current day"""
        today = datetime.datetime.now()
        return today.replace(hour=0, minute=0, second=0, microsecond=0)

    def is_new_week(self) -> bool:
        """Check if we're in a new week since last update"""
        current_week_start = self._get_week_start()
        return current_week_start > self.current_week_start

    def is_new_day(self) -> bool:
        """Check if we're in a new day since last update"""
        current_day_start = self._get_day_start()
        last_update_day_start = self.last_updated.replace(hour=0, minute=0, second=0, microsecond=0)
        return current_day_start > last_update_day_start

    def reset_weekly_earnings(self):
        """Reset weekly earnings for new week"""
        self.weekly_earnings = 0.0
        self.completed_deliveries_week = 0
        self.current_week_start = self._get_week_start()

    def reset_daily_earnings(self):
        """Reset daily earnings for new day"""
        self.daily_earnings = 0.0
        self.completed_deliveries_today = 0

    def add_delivery_earning(self, delivery_fee: float):
        """Add earnings from a completed delivery (50% of delivery fee)"""
        # Check if we need to reset for new week/day
        if self.is_new_week():
            self.reset_weekly_earnings()
        if self.is_new_day():
            self.reset_daily_earnings()

        # Calculate personnel earnings (50% of delivery fee)
        personnel_earning = delivery_fee * 0.5

        # Add to earnings
        self.daily_earnings += personnel_earning
        self.weekly_earnings += personnel_earning
        self.total_lifetime_earnings += personnel_earning
        self.completed_deliveries_today += 1
        self.completed_deliveries_week += 1
        self.last_updated = datetime.datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert earnings to dictionary for storage"""
        return {
            "personnel_id": self.personnel_id,
            "daily_earnings": self.daily_earnings,
            "weekly_earnings": self.weekly_earnings,
            "current_week_start": self.current_week_start.strftime("%Y-%m-%d %H:%M:%S"),
            "last_updated": self.last_updated.strftime("%Y-%m-%d %H:%M:%S"),
            "total_lifetime_earnings": self.total_lifetime_earnings,
            "completed_deliveries_today": self.completed_deliveries_today,
            "completed_deliveries_week": self.completed_deliveries_week,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DeliveryPersonnelEarnings":
        """Create earnings from dictionary"""
        earnings = cls(data.get("personnel_id", ""))
        earnings.daily_earnings = data.get("daily_earnings", 0.0)
        earnings.weekly_earnings = data.get("weekly_earnings", 0.0)
        earnings.total_lifetime_earnings = data.get("total_lifetime_earnings", 0.0)
        earnings.completed_deliveries_today = data.get("completed_deliveries_today", 0)
        earnings.completed_deliveries_week = data.get("completed_deliveries_week", 0)

        # Parse datetime fields
        try:
            earnings.current_week_start = datetime.datetime.strptime(
                data.get("current_week_start", ""), "%Y-%m-%d %H:%M:%S"
            )
        except:
            earnings.current_week_start = earnings._get_week_start()

        try:
            earnings.last_updated = datetime.datetime.strptime(
                data.get("last_updated", ""), "%Y-%m-%d %H:%M:%S"
            )
        except:
            earnings.last_updated = datetime.datetime.now()

        # Auto-reset if needed
        if earnings.is_new_week():
            earnings.reset_weekly_earnings()
        if earnings.is_new_day():
            earnings.reset_daily_earnings()

        return earnings


class UserProfile:
    """Represents a user profile in the system"""

    def __init__(self, user_id: int):
        self.user_id = user_id
        self.name: Optional[str] = None
        self.phone_number: Optional[str] = None
        self.points: int = 0

        self.order_history: List[Dict[str, Any]] = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert the user profile to a dictionary for storage"""
        return {
            "user_id": self.user_id,
            "name": self.name,
            "phone_number": self.phone_number,
            "points": self.points,
            "order_history": self.order_history,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserProfile":
        """Create a user profile from a dictionary"""
        profile = cls(data.get("user_id", 0))
        for key, value in data.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        return profile
