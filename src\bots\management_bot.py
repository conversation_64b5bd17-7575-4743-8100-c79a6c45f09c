"""
Management Bot for Wiz Aroma Delivery System
Comprehensive management interface for delivery personnel, analytics, and system oversight.
Access restricted to authorized management Telegram IDs.
"""

import telebot
from telebot import types
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import sys
import os
import time
from collections import defaultdict, deque
import json

from src.config import logger

from src.firebase_db import get_data, set_data, delete_data, update_data
from src.data_models import (
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    DeliveryPersonnel
)
from src.utils.delivery_personnel_utils import (
    create_delivery_personnel,
    verify_delivery_personnel,
    get_delivery_personnel_by_telegram_id,
    get_all_delivery_personnel,
    remove_delivery_personnel
)
from src.utils.earnings_utils import (
    get_personnel_earnings_summary,
    get_all_personnel_earnings,
    update_personnel_earnings,
    get_weekly_earnings_report
)
from src.utils.helpers import is_admin
from src.utils.financial_calculations import (
    calculate_revenue_breakdown,
    calculate_company_profit,
    calculate_personnel_earnings,
    format_financial_summary
)

# Management Bot Configuration
# General management access (personnel, analytics, reports)
try:
    import ast
    import os
    # Get system admin ID from environment - REQUIRED
    system_admin_id = os.getenv("SYSTEM_ADMIN_ID")
    if not system_admin_id:
        logger.error("SYSTEM_ADMIN_ID environment variable is required for management bot!")
        logger.error("Please set SYSTEM_ADMIN_ID in your .env file")
        raise ValueError("Missing required SYSTEM_ADMIN_ID environment variable")

    default_system_admin = int(system_admin_id)
    # Use only the system admin ID as default - no hardcoded secondary admin
    default_ids = f"[{default_system_admin}]"

    MANAGEMENT_BOT_AUTHORIZED_IDS = ast.literal_eval(
        os.getenv("MANAGEMENT_BOT_AUTHORIZED_IDS", default_ids)
    )
except (SyntaxError, ValueError) as e:
    logger.error(f"Error parsing MANAGEMENT_BOT_AUTHORIZED_IDS: {e}")
    logger.error("Please check the format of MANAGEMENT_BOT_AUTHORIZED_IDS in your .env file")
    # Exit gracefully rather than using hardcoded fallbacks
    raise ValueError("Invalid MANAGEMENT_BOT_AUTHORIZED_IDS configuration")
# Note: System management operations (reset/cleanup) are restricted to primary admin only

# Get management bot instance from bot_instance.py to avoid circular imports
def get_management_bot():
    """Get the management bot instance"""
    from src.bot_instance import management_bot
    return management_bot

management_bot = get_management_bot()

# Print bot info for verification
try:
    bot_info = management_bot.get_me()
    print(f"[BOT INFO] Username: @{bot_info.username}, ID: {bot_info.id}")
except Exception as e:
    print(f"[BOT INFO] Could not fetch bot info: {e}")

# Export the management bot instance for use in main.py
__all__ = ['management_bot', 'register_management_bot_handlers']

# Simple in-memory rate limiter
RATE_LIMIT = 10  # actions
RATE_PERIOD = 60  # seconds
user_action_times = defaultdict(lambda: deque(maxlen=RATE_LIMIT))

# Validation functions
def validate_personnel_id(personnel_id: str) -> bool:
    """Validate personnel ID format"""
    if not personnel_id or not isinstance(personnel_id, str):
        return False
    # Check if it's a valid Firestore document ID format
    return len(personnel_id) > 0 and personnel_id.replace('_', '').replace('-', '').isalnum()

def validate_phone_number(phone: str) -> bool:
    """Validate phone number format"""
    if not phone or not isinstance(phone, str):
        return False
    # Basic validation: should start with + or 0 and be at least 10 characters
    return (phone.startswith('+') or phone.startswith('0')) and len(phone) >= 10

def validate_telegram_id_format(telegram_id: str) -> bool:
    """Validate Telegram ID format (basic check)"""
    if not telegram_id or not isinstance(telegram_id, str):
        return False
    # Telegram IDs are numeric
    return telegram_id.isdigit() and len(telegram_id) >= 5

def validate_name(name: str) -> bool:
    if not name or not isinstance(name, str):
        return False

    # Strip whitespace and check minimum length
    name = name.strip()
    if len(name) < 2:
        return False

    # Check maximum length to prevent abuse
    if len(name) > 50:
        return False

    # Enhanced character validation - allow letters, numbers, spaces, and common punctuation
    # Allowed characters: letters (any language), numbers, spaces, hyphens, apostrophes, periods, commas
    allowed_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 -'.,")

    # Check if all characters are allowed or are Unicode letters (for international names)
    for char in name:
        if char not in allowed_chars and not char.isalpha():
            return False

    # Prevent names that are only numbers or special characters
    if name.replace(' ', '').replace('-', '').replace("'", '').replace('.', '').replace(',', '').isdigit():
        return False

    # Prevent names that are only special characters
    if all(c in " -'.,0123456789" for c in name):
        return False

    # Must contain at least one letter
    if not any(c.isalpha() for c in name):
        return False

    return True

def escape_markdown(text: str) -> str:
    """Escape special Markdown characters to prevent parsing errors"""
    if not text or not isinstance(text, str):
        return str(text) if text is not None else "N/A"

    # Characters that need to be escaped in Telegram Markdown
    # NOTE: Removed '.' from special_chars to fix decimal number display
    special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '!']

    escaped_text = str(text)
    for char in special_chars:
        escaped_text = escaped_text.replace(char, f'\\{char}')

    return escaped_text

def format_number(value: float, decimal_places: int = 2) -> str:
    """Format numbers for display without escaping decimal points"""
    if value is None:
        return "0.00"
    try:
        return f"{float(value):.{decimal_places}f}"
    except (ValueError, TypeError):
        return "0.00"

def safe_format_message(template: str, **kwargs) -> str:
    """Safely format a message with escaped parameters"""
    try:
        # Escape all string parameters
        escaped_kwargs = {}
        for key, value in kwargs.items():
            if isinstance(value, str):
                escaped_kwargs[key] = escape_markdown(value)
            else:
                escaped_kwargs[key] = value

        return template.format(**escaped_kwargs)
    except Exception as e:
        logger.error(f"Error formatting message: {e}")
        return "Error formatting message. Please try again."

def validate_analytics_data(data, data_type="orders"):
    """Validate analytics data before processing"""
    try:
        if not data:
            logger.warning(f"No {data_type} data available for analytics")
            return {}

        if not isinstance(data, dict):
            logger.error(f"Invalid {data_type} data type: expected dict, got {type(data)}")
            return {}

        # Validate data structure
        valid_data = {}
        for key, value in data.items():
            if isinstance(value, dict):
                valid_data[key] = value
            else:
                logger.warning(f"Invalid {data_type} entry: {key} - {type(value)}")

        return valid_data
    except Exception as e:
        logger.error(f"Error validating {data_type} data: {e}")
        return {}

def safe_get_numeric_value(data, key, default=0):
    """Safely extract numeric values from data with validation"""
    try:
        value = data.get(key, default)
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            try:
                return float(value)
            except ValueError:
                logger.warning(f"Could not convert {key} value '{value}' to float")
                return default
        else:
            logger.warning(f"Invalid {key} value type: {type(value)}")
            return default
    except Exception as e:
        logger.error(f"Error getting numeric value for {key}: {e}")
        return default

def safe_calculate_percentage(numerator, denominator, default=0):
    """Safely calculate percentage with division by zero protection"""
    try:
        if denominator == 0:
            return default
        return (numerator / denominator) * 100
    except Exception as e:
        logger.error(f"Error calculating percentage: {e}")
        return default

def handle_analytics_error(call, error_message, function_name):
    """Centralized error handling for analytics functions"""
    try:
        logger.error(f"Error in {function_name}: {error_message}")

        # Try to send error message to user
        try:
            management_bot.answer_callback_query(
                call.id,
                f"❌ Error loading {function_name.replace('_', ' ')}",
                show_alert=True
            )
        except:
            pass

        # Try to edit message with error info
        try:
            error_text = f"""
❌ **Analytics Error**

An error occurred while loading analytics data.

**Error:** {function_name.replace('_', ' ')}
**Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Please try again or contact support if the issue persists.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔄 Retry", callback_data=f"analytics_{function_name.split('_')[-1]}"),
                types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
            )

            # Use safe message editing for error display
            if not safe_edit_message(call, error_text, keyboard):
                management_bot.answer_callback_query(call.id, "❌ Error displaying message. Please try again.", show_alert=True)
        except Exception as edit_error:
            logger.error(f"Could not edit message with error info: {edit_error}")

    except Exception as e:
        logger.error(f"Error in error handler: {e}")

# Message Length and Display Optimization Functions
def validate_message_length(text, max_length=4000):
    """Validate message length and return if it's within Telegram limits"""
    return len(text) <= max_length

def truncate_message_content(text, max_length=4000, preserve_structure=True):
    """Truncate message content while preserving structure"""
    if len(text) <= max_length:
        return text, False  # No truncation needed

    if preserve_structure:
        # Find a good breaking point (end of a section)
        lines = text.split('\n')
        truncated_lines = []
        current_length = 0

        for line in lines:
            line_length = len(line) + 1  # +1 for newline
            if current_length + line_length > max_length - 100:  # Leave space for truncation notice
                break
            truncated_lines.append(line)
            current_length += line_length

        truncated_text = '\n'.join(truncated_lines)
        truncated_text += "\n\n📄 **Content Truncated**\nUse 'Show More' for complete data."
        return truncated_text, True
    else:
        # Simple truncation
        return text[:max_length-50] + "\n\n📄 **Content Truncated**", True

def create_paginated_content(full_content, page_size=3500):
    """Split content into pages for pagination"""
    if len(full_content) <= page_size:
        return [full_content]

    lines = full_content.split('\n')
    pages = []
    current_page = []
    current_length = 0

    for line in lines:
        line_length = len(line) + 1
        if current_length + line_length > page_size and current_page:
            pages.append('\n'.join(current_page))
            current_page = [line]
            current_length = line_length
        else:
            current_page.append(line)
            current_length += line_length

    if current_page:
        pages.append('\n'.join(current_page))

    return pages

def optimize_personnel_list(personnel_data, max_items=5, show_summary=True):
    """Optimize personnel list display to reduce message length"""
    if not personnel_data:
        return "• No personnel data available", 0

    # Sort by some criteria (e.g., earnings, status)
    sorted_personnel = sorted(personnel_data, key=lambda x: x.get('earnings', 0), reverse=True)

    # Show only top items
    displayed_items = sorted_personnel[:max_items]
    remaining_count = len(sorted_personnel) - max_items

    # Create concise list
    personnel_list = []
    for i, person in enumerate(displayed_items, 1):
        name = person.get('name', 'Unknown')
        earnings = person.get('earnings', 0)
        status = person.get('status', 'offline')

        # Use emoji for status instead of text
        status_emoji = "🟢" if status == "available" else "🔴" if status == "busy" else "⚫"

        # Concise format
        personnel_list.append(f"{i}. {status_emoji} {name}: {earnings:.1f} birr")

    result = '\n'.join(personnel_list)

    if remaining_count > 0:
        result += f"\n... and {remaining_count} more personnel"

    return result, len(sorted_personnel)

def add_refresh_timestamp(text):
    """Add timestamp to ensure content changes on refresh"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    return f"{text}\n\n🕐 **Last Updated:** {timestamp}"

def content_has_changed(current_text, new_text, current_markup, new_markup):
    """Check if message content or markup has actually changed"""
    # Remove timestamps for comparison
    import re
    current_clean = re.sub(r'\n\n🕐 \*\*Last Updated:\*\* \d{2}:\d{2}:\d{2}', '', current_text or '')
    new_clean = re.sub(r'\n\n🕐 \*\*Last Updated:\*\* \d{2}:\d{2}:\d{2}', '', new_text or '')

    # Compare text content
    text_changed = current_clean.strip() != new_clean.strip()

    # Compare markup (simplified comparison)
    markup_changed = str(current_markup) != str(new_markup)

    return text_changed or markup_changed

def safe_edit_message(call, text, keyboard, parse_mode='Markdown', max_retries=3):
    """Safely edit message with comprehensive fallback options and content change detection"""
    original_text = text

    # Get current message content for comparison
    current_content = getattr(call.message, 'text', '') or getattr(call.message, 'caption', '')
    current_markup = call.message.reply_markup

    # Validate message length first
    if not validate_message_length(text):
        text, was_truncated = truncate_message_content(text)
        if was_truncated:
            # Add "Show More" button if content was truncated
            if keyboard:
                keyboard.add(types.InlineKeyboardButton("📄 Show More", callback_data="show_more_details"))

    # Check if content actually changed before attempting edit
    if content_has_changed(current_content, text, current_markup, keyboard):
        # Add timestamp to ensure content changes for refresh operations
        if hasattr(call, 'data') and ('refresh' in call.data or 'analytics' in call.data):
            text = add_refresh_timestamp(text)
    else:
        # Content is identical, don't attempt edit
        logger.info("Content unchanged, skipping message edit")
        return True

    for attempt in range(max_retries):
        try:
            management_bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=keyboard,
                parse_mode=parse_mode
            )
            return True
        except Exception as e:
            error_msg = str(e).lower()
            logger.warning(f"Message edit attempt {attempt + 1} failed: {error_msg}")

            if "message is not modified" in error_msg:
                # Content is identical, add random element to force change
                import random
                text = f"{text}\n🔄 Updated {random.randint(1, 999)}"
                continue
            elif "message too long" in error_msg or "entities" in error_msg:
                # Further truncate the message or remove markdown
                if attempt == 0:
                    text, _ = truncate_message_content(text, max_length=3500)
                elif attempt == 1:
                    # Remove markdown formatting
                    text = text.replace('*', '').replace('_', '').replace('`', '')
                    parse_mode = None
                else:
                    # Ultra-short fallback
                    text = create_emergency_fallback_message(original_text)
                continue
            elif "bad request" in error_msg or "can't parse" in error_msg:
                # Remove markdown and try again
                text = text.replace('*', '').replace('_', '').replace('`', '')
                parse_mode = None
                continue
            elif attempt == max_retries - 1:
                # Last attempt failed, try sending new message
                return send_fallback_message(call, original_text, keyboard)
            else:
                continue

    return False

def create_emergency_fallback_message(original_text):
    """Create ultra-short emergency fallback message"""
    lines = original_text.split('\n')
    # Keep only the title and first few important lines
    emergency_lines = []
    for line in lines[:10]:  # First 10 lines only
        if line.strip() and not line.startswith('•'):
            emergency_lines.append(line.strip())
        if len('\n'.join(emergency_lines)) > 500:
            break

    emergency_text = '\n'.join(emergency_lines)
    emergency_text += "\n\n⚠️ Simplified view due to display limits."
    return emergency_text

def send_fallback_message(call, original_text, keyboard):
    """Send new message as fallback when editing fails"""
    try:
        # Try with simplified content first
        simplified_text = create_emergency_fallback_message(original_text)
        simplified_text = add_refresh_timestamp(simplified_text)

        management_bot.send_message(
            call.message.chat.id,
            f"📊 **Analytics Update**\n\n{simplified_text}",
            reply_markup=keyboard,
            parse_mode=None  # No markdown to avoid parsing issues
        )
        return True
    except Exception as e:
        logger.error(f"Fallback message also failed: {e}")
        try:
            # Ultimate fallback - plain text error message
            error_keyboard = types.InlineKeyboardMarkup()
            error_keyboard.add(
                types.InlineKeyboardButton("🔄 Try Again", callback_data="mgmt_analytics"),
                types.InlineKeyboardButton("🏠 Main Menu", callback_data="mgmt_main")
            )

            management_bot.send_message(
                call.message.chat.id,
                "⚠️ Analytics Display Error\n\nUnable to display analytics data due to formatting issues. Please try again or contact support.",
                reply_markup=error_keyboard
            )
            return True
        except:
            return False

def handle_display_error(call, error_type="general"):
    """Handle display errors with user-friendly messages"""
    try:
        error_messages = {
            "length": "📄 Content too long for display. Use 'Details' buttons for full data.",
            "format": "🔧 Display formatting issue. Trying simplified view...",
            "network": "🌐 Connection issue. Please try refreshing.",
            "general": "⚠️ Display error occurred. Please try again."
        }

        message = error_messages.get(error_type, error_messages["general"])

        management_bot.answer_callback_query(
            call.id,
            message,
            show_alert=True
        )

        # Provide navigation options
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Retry", callback_data="mgmt_analytics"),
            types.InlineKeyboardButton("🏠 Main Menu", callback_data="mgmt_main")
        )

        try:
            management_bot.edit_message_text(
                f"⚠️ **Display Issue**\n\n{message}\n\nPlease use the buttons below to continue.",
                call.message.chat.id,
                call.message.message_id,
                reply_markup=keyboard
            )
        except:
            # If edit fails, send new message
            management_bot.send_message(
                call.message.chat.id,
                f"⚠️ **Display Issue**\n\n{message}\n\nPlease use the buttons below to continue.",
                reply_markup=keyboard
            )

    except Exception as e:
        logger.error(f"Error in error handler: {e}")

def ensure_analytics_accessibility(call):
    """Ensure analytics remain accessible even when display issues occur"""
    try:
        # Create a simplified analytics menu that always works
        text = """📊 **Analytics Dashboard**

Choose an analytics option:

**Quick Views:**
• Daily Summary
• Weekly Summary
• Monthly Summary
• Personnel Payroll

**Note:** If you experience display issues, try the simplified views or contact support."""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📅 Daily", callback_data="analytics_daily"),
            types.InlineKeyboardButton("📊 Weekly", callback_data="analytics_weekly")
        )
        keyboard.add(
            types.InlineKeyboardButton("📈 Monthly", callback_data="analytics_monthly"),
            types.InlineKeyboardButton("💰 Payroll", callback_data="analytics_payroll")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Main Menu", callback_data="mgmt_main")
        )

        management_bot.send_message(
            call.message.chat.id,
            text,
            reply_markup=keyboard
        )

    except Exception as e:
        logger.error(f"Error ensuring analytics accessibility: {e}")
        # Ultimate fallback
        try:
            management_bot.send_message(
                call.message.chat.id,
                "📊 Analytics system temporarily unavailable. Please contact support."
            )
        except:
            pass

# ============================================================================
# REAL-TIME DATA SYNCHRONIZATION MODULE
# ============================================================================

def refresh_personnel_data():
    """Force refresh personnel data from Firebase for real-time updates"""
    try:
        # Clear any cached data and fetch fresh from Firebase
        personnel_data = get_data("delivery_personnel") or {}
        logger.info(f"Refreshed personnel data: {len(personnel_data)} personnel found")
        return personnel_data
    except Exception as e:
        logger.error(f"Error refreshing personnel data: {e}")
        return {}

def refresh_availability_data():
    """Force refresh availability data from Firebase for real-time updates"""
    try:
        availability_data = get_data("delivery_personnel_availability") or {}
        logger.info(f"Refreshed availability data: {len(availability_data)} records found")
        return availability_data
    except Exception as e:
        logger.error(f"Error refreshing availability data: {e}")
        return {}

def refresh_analytics_data():
    """Force refresh all analytics data from Firebase for real-time reporting"""
    try:
        # Check and execute time-based resets before refreshing analytics
        from src.utils.time_based_reset_utils import check_and_execute_time_based_resets
        reset_results = check_and_execute_time_based_resets()

        # Log any resets that were executed
        executed_resets = [k for k, v in reset_results.items() if v]
        if executed_resets:
            logger.info(f"🔄 Executed time-based resets during analytics refresh: {', '.join(executed_resets)}")

        # Sync analytics counters with current order data
        try:
            sync_analytics_counters_with_orders()
        except Exception as e:
            logger.error(f"Error syncing analytics counters: {e}")

        data = {
            'completed_orders': get_data("completed_orders") or {},
            'confirmed_orders': get_data("confirmed_orders") or {},
            'assignments': get_data("delivery_personnel_assignments") or {},
            'earnings': get_data("delivery_personnel_earnings") or {},
            'personnel': get_data("delivery_personnel") or {}
        }
        logger.info(f"Refreshed analytics data: {sum(len(v) for v in data.values())} total records")
        return data
    except Exception as e:
        logger.error(f"Error refreshing analytics data: {e}")
        return {}

def invalidate_personnel_cache(personnel_id: str = None):
    """Invalidate cached personnel data to force refresh"""
    try:
        if personnel_id:
            # Invalidate specific personnel cache
            cache_paths = [
                f"delivery_personnel_capacity_tracking/{personnel_id}",
                f"delivery_personnel_cache/{personnel_id}"
            ]
            for path in cache_paths:
                delete_data(path)
            logger.info(f"Invalidated cache for personnel {personnel_id}")
        else:
            # Invalidate all personnel cache
            delete_data("delivery_personnel_capacity_tracking")
            delete_data("delivery_personnel_cache")
            logger.info("Invalidated all personnel cache")
    except Exception as e:
        logger.error(f"Error invalidating personnel cache: {e}")


def sync_analytics_counters_with_orders():
    """Synchronize analytics counters with current order data"""
    try:
        from src.utils.analytics_counter_system import get_analytics_counters, get_current_time_periods

        logger.info("🔄 Syncing analytics counters with current order data...")

        # Get current order data
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Get current time periods
        current_periods = get_current_time_periods()

        # Calculate actual counts for each period
        period_configs = {
            'daily': current_periods['current_date'],
            'weekly': current_periods['current_week_start'],
            'monthly': current_periods['current_month_start']
        }

        # Get current counters
        counters = get_analytics_counters()

        for period_type, period_start in period_configs.items():
            # Filter orders for this period
            filtered_orders = filter_orders_by_time_period_with_resets(
                {'completed_orders': completed_orders_data, 'confirmed_orders': confirmed_orders_data},
                period_type
            )

            # Categorize orders
            categorized_orders = categorize_orders_by_status(
                filtered_orders.get('completed_orders', {}),
                filtered_orders.get('confirmed_orders', {}),
                assignments_data
            )

            # Calculate actual counts
            actual_stats = calculate_category_percentages(categorized_orders)

            # Update counters if they don't match (within reasonable tolerance)
            current_counters = counters.get(period_type, {})
            counter_total = (
                current_counters.get('complete_orders', 0) +
                current_counters.get('incomplete_orders', 0) +
                current_counters.get('issue_orders', 0)
            )

            actual_total = actual_stats.get('total_count', 0)

            # If there's a significant discrepancy, log it
            if abs(counter_total - actual_total) > 0:
                logger.info(f"📊 {period_type.title()} counter sync: Counter={counter_total}, Actual={actual_total}")

        logger.info("✅ Analytics counter sync completed")

    except Exception as e:
        logger.error(f"Error syncing analytics counters: {e}")


def track_order_status_change(order_id: str, old_status: str, new_status: str):
    """Track order status changes and update analytics counters accordingly"""
    try:
        # Map order statuses to analytics categories
        # Note: Only 'customer_confirmed' should be considered truly complete
        status_map = {
            'completed': 'incomplete',  # Delivery completed but not customer confirmed
            'customer_confirmed': 'complete',  # Truly complete - customer confirmed
            'confirmed': 'incomplete',  # Confirmed but not yet completed
            'assigned': 'incomplete',   # Assigned to delivery personnel
            'pending': 'incomplete',    # Pending assignment
            'issue': 'issue',          # Has issues/problems
            'cancelled': 'issue'       # Cancelled orders
        }

        old_category = status_map.get(old_status)
        new_category = status_map.get(new_status)

        if old_category and new_category and old_category != new_category:
            # Update counters for the change
            logger.info(f"📊 Order {order_id} status change: {old_status} -> {new_status} ({old_category} -> {new_category})")

            # Note: For now, we'll rely on the sync function to maintain accuracy
            # In the future, we could implement increment/decrement logic here

    except Exception as e:
        logger.error(f"Error tracking order status change: {e}")


def filter_orders_by_time_period_with_resets(orders_data, period_type='daily'):
    """Filter orders by time period considering time-based resets

    Args:
        orders_data: Dictionary of orders to filter, can be a single dict or nested dict with 'completed_orders' and 'confirmed_orders'
        period_type: 'daily', 'weekly', or 'monthly'

    Returns:
        Filtered orders dictionary
    """
    try:
        from src.utils.time_based_reset_utils import get_current_time_periods

        current_periods = get_current_time_periods()

        # Get reset tracking data to determine filter boundaries
        tracking_data = get_data("time_based_reset_tracking") or {}

        # Determine the date boundary based on period type
        if period_type == 'daily':
            current_date = current_periods['current_date']
            last_reset = tracking_data.get('last_daily_reset', current_date)
        elif period_type == 'weekly':
            current_week_start = current_periods['current_week_start']
            last_reset = tracking_data.get('last_weekly_reset', current_week_start)
        elif period_type == 'monthly':
            current_month_start = current_periods['current_month_start']
            last_reset = tracking_data.get('last_monthly_reset', current_month_start)
        else:
            logger.error(f"Invalid period_type: {period_type}")
            return orders_data

        # Check if orders_data has nested structure (completed_orders and confirmed_orders)
        if isinstance(orders_data, dict) and ('completed_orders' in orders_data or 'confirmed_orders' in orders_data):
            # Handle nested structure
            filtered_orders = {}

            # Filter completed orders using completed_at or customer_confirmed_at
            if 'completed_orders' in orders_data:
                filtered_orders['completed_orders'] = {}
                for order_id, order in orders_data['completed_orders'].items():
                    # Use completed_at or customer_confirmed_at for completed orders
                    order_date = (order.get('customer_confirmed_at', '') or
                                order.get('completed_at', '') or
                                order.get('confirmed_at', ''))[:10]
                    if order_date and order_date >= last_reset:
                        filtered_orders['completed_orders'][order_id] = order

            # Filter confirmed orders using created_at (original order creation date)
            if 'confirmed_orders' in orders_data:
                filtered_orders['confirmed_orders'] = {}
                for order_id, order in orders_data['confirmed_orders'].items():
                    # Use created_at for filtering by original order creation time
                    order_date = order.get('created_at', '')[:10]
                    if order_date and order_date >= last_reset:
                        filtered_orders['confirmed_orders'][order_id] = order

            return filtered_orders
        else:
            # Handle flat structure (single order collection)
            filtered_orders = {}
            for order_id, order in orders_data.items():
                # Try to determine the appropriate date field, prioritizing created_at for time filtering
                order_date = (order.get('customer_confirmed_at', '') or
                            order.get('completed_at', '') or
                            order.get('created_at', '') or
                            order.get('confirmed_at', ''))[:10]
                if order_date and order_date >= last_reset:
                    filtered_orders[order_id] = order

            return filtered_orders

    except Exception as e:
        logger.error(f"Error filtering orders by time period: {e}")
        return orders_data  # Return original data on error

# ============================================================================
# DATA MANAGEMENT SYSTEM - FIREBASE OPERATIONS MODULE
# ============================================================================

# Enhanced authorization using access control module
from src.utils.access_control import (
    is_admin,
    require_admin,
    rate_limit,
    AccessDeniedError,
    RateLimitExceededError,
    validate_telegram_id
)

def is_authorized_for_reset(user_id):
    """Check if user is authorized for reset operations with enhanced security - SYSTEM ADMIN ONLY"""
    try:
        validated_id = validate_telegram_id(user_id)
        if not validated_id:
            logger.warning(f"Invalid user ID format for reset authorization: {user_id}")
            return False

        # System management operations are restricted to primary admin only
        from src.config import SYSTEM_ADMIN_ID
        is_system_admin = validated_id == SYSTEM_ADMIN_ID

        if not is_system_admin:
            logger.warning(f"System management access denied for user {validated_id} - requires primary admin ({SYSTEM_ADMIN_ID})")

        return is_system_admin
    except Exception as e:
        logger.error(f"Error checking reset authorization for user {user_id}: {e}")
        return False

def safe_get_current_timestamp():
    """Safely get current timestamp with fallback"""
    try:
        return datetime.now().isoformat()
    except Exception as e:
        logger.error(f"Error getting current timestamp: {e}")
        # Fallback to basic timestamp
        import time
        return str(int(time.time()))

def safe_parse_datetime(date_string, fallback_format='%Y-%m-%d'):
    """Safely parse datetime string with multiple format support and debugging"""
    if not date_string:
        logger.warning("Empty date string provided to safe_parse_datetime")
        return None

    try:
        # List of common timestamp formats to try
        formats_to_try = [
            '%Y-%m-%d %H:%M:%S',      # 2025-07-10 15:05:12
            '%Y-%m-%d %H:%M:%S.%f',   # 2025-07-10 15:05:12.123456
            '%Y-%m-%dT%H:%M:%S',      # 2025-07-10T15:05:12
            '%Y-%m-%dT%H:%M:%S.%f',   # 2025-07-10T15:05:12.123456
            '%Y-%m-%dT%H:%M:%SZ',     # 2025-07-10T15:05:12Z
            '%Y-%m-%dT%H:%M:%S.%fZ',  # 2025-07-10T15:05:12.123456Z
            '%Y-%m-%d',               # 2025-07-10
            fallback_format           # Custom fallback
        ]

        # Try ISO format first if it contains T or Z
        if 'T' in date_string or 'Z' in date_string:
            try:
                # Handle ISO format with timezone
                clean_string = date_string.replace('Z', '+00:00')
                return datetime.fromisoformat(clean_string)
            except Exception as iso_error:
                logger.debug(f"ISO format parsing failed for '{date_string}': {iso_error}")

        # Try each format
        for fmt in formats_to_try:
            try:
                # Truncate string to match format length if needed
                if fmt == '%Y-%m-%d' and len(date_string) > 10:
                    test_string = date_string[:10]
                else:
                    test_string = date_string

                parsed_dt = datetime.strptime(test_string, fmt)
                logger.debug(f"Successfully parsed '{date_string}' using format '{fmt}'")
                return parsed_dt

            except ValueError:
                continue

        # If all formats fail, log the issue and return None
        logger.warning(f"Could not parse datetime '{date_string}' with any known format")
        return None

    except Exception as e:
        logger.error(f"Unexpected error parsing datetime '{date_string}': {e}")
        return None

def log_reset_operation(operation_type, user_id, details=None):
    """Log reset operations for audit purposes with enhanced error handling"""
    try:
        timestamp = safe_get_current_timestamp()
        log_entry = {
            'timestamp': timestamp,
            'operation_type': operation_type,
            'user_id': str(user_id),
            'details': details or {},
            'status': 'initiated'
        }

        # Store in Firebase audit log
        audit_data = get_data("system_audit_log") or {}
        log_id = f"{operation_type}_{timestamp.replace(':', '-').replace('.', '-')}"
        audit_data[log_id] = log_entry

        set_data("system_audit_log", audit_data)
        logger.info(f"Reset operation logged: {operation_type} by user {user_id}")

        return log_id
    except Exception as e:
        logger.error(f"Error logging reset operation: {e}")
        # Return a fallback log ID
        import time
        return f"fallback_{operation_type}_{int(time.time())}"

def create_data_backup(collections_to_backup, backup_name):
    """Create timestamped backup of specified collections with enhanced error handling"""
    try:
        # Use safe timestamp generation
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        except Exception as e:
            logger.warning(f"Error formatting timestamp: {e}")
            import time
            timestamp = f"backup_{int(time.time())}"

        backup_data = {
            'backup_timestamp': timestamp,
            'backup_name': backup_name,
            'collections': {}
        }

        total_records = 0
        for collection_name in collections_to_backup:
            collection_data = get_data(collection_name) or {}
            backup_data['collections'][collection_name] = collection_data
            total_records += len(collection_data)
            logger.info(f"Backed up {len(collection_data)} records from {collection_name}")

        # Store backup in Firebase
        backup_id = f"{backup_name}_{timestamp}"
        set_data(f"backups/{backup_id}", backup_data)

        logger.info(f"Backup created: {backup_id} with {total_records} total records")
        return backup_id, total_records

    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        return None, 0

def batch_clear_collections(collections_to_clear, preserve_current_day=False):
    """Clear specified collections using batch operations"""
    try:
        cleared_counts = {}
        today = datetime.now().strftime("%Y-%m-%d")

        for collection_name in collections_to_clear:
            collection_data = get_data(collection_name) or {}
            original_count = len(collection_data)

            if preserve_current_day and collection_name in ['confirmed_orders']:
                # Preserve orders from today
                preserved_data = {}
                for order_id, order in collection_data.items():
                    confirmed_at = order.get('confirmed_at', '')
                    if confirmed_at.startswith(today):
                        preserved_data[order_id] = order

                # Use safe Firebase operation with validation
                if safe_firebase_set_with_verification(collection_name, preserved_data):
                    cleared_count = original_count - len(preserved_data)
                    logger.info(f"Cleared {cleared_count} records from {collection_name}, preserved {len(preserved_data)} current day records")
                else:
                    logger.error(f"Failed to clear collection {collection_name}")
                    cleared_count = 0
            else:
                # Clear entire collection using safe operation
                if safe_firebase_set_with_verification(collection_name, {}):
                    cleared_count = original_count
                    logger.info(f"Cleared all {cleared_count} records from {collection_name}")
                else:
                    logger.error(f"Failed to clear collection {collection_name}")
                    cleared_count = 0

            cleared_counts[collection_name] = cleared_count

        # Invalidate all caches after clearing collections
        invalidate_personnel_cache()

        return cleared_counts

    except Exception as e:
        logger.error(f"Error in batch clear operations: {e}")
        return {}

def reset_personnel_earnings_only():
    """Reset only earnings data while preserving personnel profiles"""
    try:
        # Clear earnings data but keep personnel profiles
        earnings_data = get_data("delivery_personnel_earnings") or {}

        # Reset all earnings to zero but keep structure
        reset_earnings = {}
        for personnel_id, earnings_record in earnings_data.items():
            reset_earnings[personnel_id] = {
                'total_lifetime_earnings': 0,
                'weekly_earnings': 0,
                'last_reset_date': datetime.now().isoformat(),
                'earnings_history': []
            }

        # Use safe Firebase operation for earnings reset
        try:
            set_data("delivery_personnel_earnings", reset_earnings)

            # Custom verification for earnings reset
            verification = get_data("delivery_personnel_earnings") or {}
            if len(verification) == len(reset_earnings):
                logger.info(f"✅ Successfully reset earnings for {len(reset_earnings)} personnel")
            else:
                logger.error(f"❌ Verification failed for delivery_personnel_earnings: expected {len(reset_earnings)}, got {len(verification)}")
                return False
        except Exception as e:
            logger.error(f"Failed to reset personnel earnings: {e}")
            return False

        # Reset assignment counts in personnel data
        personnel_data = get_data("delivery_personnel") or {}
        for personnel_id, person in personnel_data.items():
            if 'assignment_count' in person:
                person['assignment_count'] = 0
            if 'completed_deliveries' in person:
                person['completed_deliveries'] = 0

        # Use safe Firebase operation for personnel data update
        try:
            set_data("delivery_personnel", personnel_data)
            logger.info(f"✅ Successfully reset assignment counts for {len(personnel_data)} personnel")
        except Exception as e:
            logger.error(f"Failed to reset personnel assignment counts: {e}")
            return False

        # Invalidate all caches after earnings reset
        invalidate_personnel_cache()

        logger.info("Personnel earnings reset completed while preserving profiles")
        return len(reset_earnings)

    except Exception as e:
        logger.error(f"Error resetting personnel earnings: {e}")
        return 0

def safe_cleanup_operation(collection_name, data, operation_name):
    """Ensure atomic operations and verify success for cleanup operations"""
    try:
        # Perform the operation
        set_data(collection_name, data)

        # Verify the operation succeeded by reading back the data
        verification = get_data(collection_name)

        # Compare the data to ensure it was saved correctly
        if verification == data:
            logger.info(f"✅ {operation_name} - Successfully updated {collection_name}")
            return True
        else:
            logger.error(f"❌ {operation_name} - Verification failed for {collection_name}")
            logger.error(f"Expected {len(data)} items, got {len(verification or {})}")
            return False

    except Exception as e:
        logger.error(f"❌ {operation_name} - Cleanup failed for {collection_name}: {e}")
        return False

def safe_firebase_set_with_verification(collection_name, data):
    """Safely set Firebase data with error handling and verification"""
    try:
        set_data(collection_name, data)

        # Verify the operation
        verification = get_data(collection_name)

        # Handle verification for empty collections (cleared data)
        if isinstance(data, dict) and not data:
            # For empty dictionaries, verification should return None or empty dict
            if verification is None or (isinstance(verification, dict) and not verification):
                logger.info(f"✅ Successfully cleared {collection_name}")
                return True
            else:
                logger.error(f"❌ Verification failed for clearing {collection_name}: still contains {len(verification or {})} items")
                return False
        else:
            # Normal verification for non-empty data
            if verification == data:
                logger.info(f"✅ Successfully set {collection_name} with {len(data)} items")
                return True
            elif isinstance(data, dict) and isinstance(verification, dict):
                # For complex data structures, check if keys and structure match
                if len(verification) == len(data) and set(verification.keys()) == set(data.keys()):
                    logger.info(f"✅ Successfully set {collection_name} with {len(data)} items (structure verified)")
                    return True
                else:
                    logger.error(f"❌ Verification failed for {collection_name}: structure mismatch")
                    logger.debug(f"Expected {len(data)} items with keys {list(data.keys())[:5]}...")
                    logger.debug(f"Got {len(verification)} items with keys {list(verification.keys())[:5]}...")
                    return False
            else:
                logger.error(f"❌ Verification failed for {collection_name}: data type mismatch")
                return False

    except Exception as e:
        logger.error(f"❌ Failed to set {collection_name}: {e}")
        return False

def archive_incomplete_orders(hours_threshold=24):
    """Archive orders that are incomplete beyond threshold with robust Firebase operations"""
    try:
        logger.info(f"🧹 Starting cleanup: archiving orders older than {hours_threshold} hours")

        # Get current data with error handling
        confirmed_orders = get_data("confirmed_orders") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        initial_confirmed_count = len(confirmed_orders)
        initial_assignments_count = len(assignments_data)

        logger.info(f"📊 Initial state: {initial_confirmed_count} confirmed orders, {initial_assignments_count} assignments")

        # Debug: Show sample of confirmed orders for analysis
        if confirmed_orders:
            sample_orders = list(confirmed_orders.items())[:3]
            for order_id, order in sample_orders:
                confirmed_at = order.get('confirmed_at', 'NO_TIMESTAMP')
                logger.info(f"🔍 Sample order {order_id}: confirmed_at = '{confirmed_at}'")
        else:
            logger.info("🔍 No confirmed orders found in database")

        try:
            current_time = datetime.now()
            logger.info(f"🕐 Current time for cleanup: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            logger.error(f"Error getting current time: {e}")
            return 0

        archived_orders = {}
        orders_to_remove = []
        processed_count = 0
        skipped_count = 0

        # Process each confirmed order with detailed logging
        for order_id, order in confirmed_orders.items():
            processed_count += 1
            confirmed_at = order.get('confirmed_at', '')

            logger.info(f"🔍 Processing order {order_id} ({processed_count}/{initial_confirmed_count})")
            logger.info(f"   confirmed_at: '{confirmed_at}'")

            if confirmed_at:
                try:
                    order_time = safe_parse_datetime(confirmed_at)
                    if order_time:
                        hours_old = (current_time - order_time).total_seconds() / 3600
                        logger.info(f"   Order age: {hours_old:.2f} hours (threshold: {hours_threshold})")

                        if hours_old > hours_threshold:
                            # Archive this order
                            archived_orders[order_id] = {
                                **order,
                                'archived_at': safe_get_current_timestamp(),
                                'reason': f'Incomplete after {hours_old:.1f} hours',
                                'cleanup_threshold': hours_threshold
                            }
                            orders_to_remove.append(order_id)
                            logger.info(f"📦 ✅ ARCHIVING order {order_id}: {hours_old:.1f} hours old")
                        else:
                            logger.info(f"   ⏭️ Skipping order {order_id}: only {hours_old:.1f} hours old")
                            skipped_count += 1
                    else:
                        logger.warning(f"   ⚠️ Could not parse timestamp for order {order_id}: '{confirmed_at}'")
                        skipped_count += 1

                except Exception as e:
                    logger.warning(f"   ❌ Error processing order {order_id}: {e}")
                    skipped_count += 1
                    continue
            else:
                logger.info(f"   ⏭️ Skipping order {order_id}: no confirmed_at timestamp")
                skipped_count += 1

        logger.info(f"📊 Processing summary: {processed_count} processed, {len(orders_to_remove)} to archive, {skipped_count} skipped")

        # Step 1: Save archived orders first (backup before deletion)
        archive_success = True
        if archived_orders:
            existing_archive = get_data("incomplete_orders_archive") or {}
            existing_archive.update(archived_orders)
            archive_success = safe_cleanup_operation(
                "incomplete_orders_archive",
                existing_archive,
                "Archive Backup"
            )

            if not archive_success:
                logger.error("❌ Failed to backup archived orders - aborting cleanup")
                return 0

        # Step 2: Remove from confirmed orders
        updated_confirmed_orders = confirmed_orders.copy()
        for order_id in orders_to_remove:
            if order_id in updated_confirmed_orders:
                del updated_confirmed_orders[order_id]

        confirmed_success = safe_cleanup_operation(
            "confirmed_orders",
            updated_confirmed_orders,
            "Confirmed Orders Cleanup"
        )

        if not confirmed_success:
            logger.error("❌ Failed to update confirmed orders - cleanup incomplete")
            return 0

        # Step 3: Clean up related assignments
        assignments_to_remove = []
        for assignment_id, assignment in assignments_data.items():
            order_number = assignment.get('order_number', '')
            # Check if this assignment belongs to an archived order
            if any(order.get('order_number') == order_number for order in archived_orders.values()):
                assignments_to_remove.append(assignment_id)

        updated_assignments = assignments_data.copy()
        for assignment_id in assignments_to_remove:
            if assignment_id in updated_assignments:
                del updated_assignments[assignment_id]

        assignments_success = safe_cleanup_operation(
            "delivery_personnel_assignments",
            updated_assignments,
            "Assignments Cleanup"
        )

        if not assignments_success:
            logger.error("❌ Failed to update assignments - cleanup incomplete")
            return 0

        # Final verification
        final_confirmed = get_data("confirmed_orders") or {}
        final_assignments = get_data("delivery_personnel_assignments") or {}

        final_confirmed_count = len(final_confirmed)
        final_assignments_count = len(final_assignments)

        logger.info(f"✅ Cleanup completed successfully:")
        logger.info(f"   📦 Archived orders: {len(archived_orders)}")
        logger.info(f"   🗑️ Assignments removed: {len(assignments_to_remove)}")
        logger.info(f"   📊 Confirmed orders: {initial_confirmed_count} → {final_confirmed_count}")
        logger.info(f"   📊 Assignments: {initial_assignments_count} → {final_assignments_count}")

        return len(archived_orders)

    except Exception as e:
        logger.error(f"❌ Critical error in archive_incomplete_orders: {e}")
        import traceback
        traceback.print_exc()
        return 0

# ============================================================================
# SEASONAL DATA RESET FEATURE
# ============================================================================

# Global state for reset confirmation process
reset_confirmation_state = {}

def show_system_management_menu(call):
    """Show system management menu with reset options"""
    try:
        # Check authorization
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(
                call.id,
                "❌ Unauthorized. Admin access required for system management.",
                show_alert=True
            )
            return

        management_bot.answer_callback_query(call.id, "🔧 Loading system management...")

        text = """🔧 **System Management**

**Data Management Options:**

**🔄 Seasonal Reset**
• Clear all analytics and financial data
• Reset personnel earnings and statistics
• Preserve personnel profiles and system config
• Create backup archive before reset

**🧹 Daily Cleanup**
• Remove incomplete/stale orders
• Clean failed delivery assignments
• Archive abandoned orders
• Optimize daily operations

**⚠️ Warning:** Reset operations are permanent and require confirmation.

**📊 Current System Status:**
• Analytics data available
• Personnel profiles active
• System configuration intact"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Reset Season Data", callback_data="reset_season_init"),
            types.InlineKeyboardButton("🧹 Daily Cleanup", callback_data="reset_daily_init")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔄 Order Reset", callback_data="order_reset_menu"),
            types.InlineKeyboardButton("📊 System Status", callback_data="system_status")
        )
        keyboard.add(
            types.InlineKeyboardButton("📋 Audit Log", callback_data="audit_log"),
            types.InlineKeyboardButton("📊 Order Status", callback_data="order_status")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error showing system management menu: {e}")
        handle_analytics_error(call, str(e), "system_management")

def initiate_seasonal_reset(call):
    """Initiate seasonal data reset with warning"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "⚠️ Seasonal reset initiated...")

        # Get current data summary
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        assignments = get_data("delivery_personnel_assignments") or {}
        personnel = get_data("delivery_personnel") or {}

        total_orders = len(completed_orders) + len(confirmed_orders)
        total_assignments = len(assignments)
        total_personnel = len(personnel)

        # Calculate total revenue that will be reset
        total_revenue = sum(
            safe_get_numeric_value(order, 'subtotal', 0) + safe_get_numeric_value(order, 'delivery_fee', 0)
            for order in completed_orders.values()
        )

        text = f"""⚠️ *SEASONAL DATA RESET WARNING*

*This operation will PERMANENTLY delete:*

📊 *Analytics Data:*
• {len(completed_orders)} completed orders
• {len(confirmed_orders)} confirmed orders
• {total_assignments} delivery assignments
• All revenue history ({total_revenue:.0f} birr)

💰 *Financial Records:*
• All profit tracking data
• Personnel earnings history
• Performance metrics and rankings

*🔒 PRESERVED DATA:*
• {total_personnel} delivery personnel profiles
• System configuration (areas, cafes, menus)
• User accounts and preferences
• Current day orders (if any)

*📋 BACKUP:*
• Complete archive will be created before reset
• Data can be recovered within 24 hours
• Archive stored with timestamp

*⚠️ THIS ACTION CANNOT BE UNDONE*

To proceed, you must type the exact confirmation phrase in the next step."""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("⚠️ I Understand - Continue", callback_data="reset_season_confirm"),
            types.InlineKeyboardButton("❌ Cancel Reset", callback_data="system_management")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error initiating seasonal reset: {e}")
        handle_analytics_error(call, str(e), "seasonal_reset_init")

def confirm_seasonal_reset(call):
    """Request typed confirmation for seasonal reset"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "🔐 Confirmation required...")

        text = """🔐 *CONFIRMATION REQUIRED*

To proceed with seasonal data reset, you must type the exact phrase:

*CONFIRM SEASON RESET*

⚠️ *Final Warning:*
• All analytics data will be permanently deleted
• Financial records will be cleared
• Personnel earnings will be reset to zero
• This action cannot be undone

*What happens next:*
1. Backup archive will be created
2. Data clearing will begin
3. Progress updates will be shown
4. Completion confirmation provided

*Type the confirmation phrase exactly as shown above, or use the Cancel button.*"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("❌ Cancel Reset", callback_data="system_management")
        )

        # Store confirmation state
        reset_confirmation_state[user_id] = {
            'type': 'seasonal_reset',
            'timestamp': datetime.now().isoformat(),
            'step': 'awaiting_confirmation'
        }

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)
        else:
            # Register next step handler for typed confirmation
            management_bot.register_next_step_handler(call.message, process_seasonal_reset_confirmation)

    except Exception as e:
        logger.error(f"Error in seasonal reset confirmation: {e}")
        handle_analytics_error(call, str(e), "seasonal_reset_confirm")

def process_seasonal_reset_confirmation(message):
    """Process typed confirmation for seasonal reset"""
    try:
        user_id = message.from_user.id
        confirmation_text = message.text.strip()

        # Check if user has pending confirmation
        if user_id not in reset_confirmation_state:
            management_bot.send_message(
                message.chat.id,
                "❌ No pending reset confirmation found. Please restart the process."
            )
            return

        # Verify confirmation phrase
        if confirmation_text != "CONFIRM SEASON RESET":
            management_bot.send_message(
                message.chat.id,
                f"❌ Incorrect confirmation phrase.\n\nYou typed: '{confirmation_text}'\nRequired: 'CONFIRM SEASON RESET'\n\nPlease try again or cancel the operation."
            )
            return

        # Confirmation successful - proceed with reset
        management_bot.send_message(
            message.chat.id,
            "✅ Confirmation received. Starting seasonal data reset...\n\n⏳ Please wait while the operation completes."
        )

        # Execute the reset
        execute_seasonal_reset(message, user_id)

    except Exception as e:
        logger.error(f"Error processing seasonal reset confirmation: {e}")
        management_bot.send_message(
            message.chat.id,
            f"❌ Error processing confirmation: {str(e)}\n\nPlease restart the reset process."
        )

def execute_seasonal_reset(message, user_id):
    """Execute the seasonal data reset operation"""
    try:
        # Clear confirmation state
        if user_id in reset_confirmation_state:
            del reset_confirmation_state[user_id]

        # Log the operation
        log_id = log_reset_operation("seasonal_reset", user_id, {
            'initiated_at': datetime.now().isoformat(),
            'user_name': message.from_user.first_name or 'Unknown'
        })

        # Step 1: Create backup
        management_bot.send_message(
            message.chat.id,
            "📦 **Step 1/4:** Creating backup archive..."
        )

        collections_to_backup = [
            "completed_orders",
            "confirmed_orders",
            "delivery_personnel_assignments",
            "delivery_personnel_earnings"
        ]

        backup_id, backup_count = create_data_backup(collections_to_backup, "seasonal_reset")

        if not backup_id:
            management_bot.send_message(
                message.chat.id,
                "❌ **Backup Failed**\n\nSeasonal reset cancelled for safety. Please try again or contact support."
            )
            return

        # Step 2: Clear analytics collections
        management_bot.send_message(
            message.chat.id,
            f"✅ Backup created: {backup_count} records\n\n🗑️ **Step 2/4:** Clearing analytics data..."
        )

        collections_to_clear = [
            "completed_orders",
            "delivery_personnel_assignments"
        ]

        cleared_counts = batch_clear_collections(collections_to_clear, preserve_current_day=True)

        # Step 3: Reset personnel earnings
        management_bot.send_message(
            message.chat.id,
            "💰 **Step 3/4:** Resetting personnel earnings..."
        )

        earnings_reset_count = reset_personnel_earnings_only()

        # Step 4: Complete and report
        management_bot.send_message(
            message.chat.id,
            "✅ **Step 4/4:** Finalizing reset..."
        )

        # Calculate totals
        total_cleared = sum(cleared_counts.values())

        completion_text = f"""🎉 *SEASONAL RESET COMPLETED*

*📊 Reset Summary:*
• Analytics records cleared: {total_cleared}
• Personnel earnings reset: {earnings_reset_count}
• Backup created: {backup_id}
• Operation logged: {log_id}

*🔒 Preserved:*
• Delivery personnel profiles
• System configuration
• User accounts
• Current day orders

*📅 New Season Started:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

*🔄 Next Steps:*
• Analytics will show zero values
• Personnel can start earning from fresh
• All counters reset to beginning
• System ready for new season operations

The system is now ready for a fresh start! 🚀"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 View Analytics", callback_data="mgmt_analytics"),
            types.InlineKeyboardButton("👥 Check Personnel", callback_data="mgmt_personnel")
        )
        keyboard.add(
            types.InlineKeyboardButton("🏠 Main Menu", callback_data="mgmt_main")
        )

        management_bot.send_message(
            message.chat.id,
            completion_text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

        # Update audit log with completion
        if log_id:
            audit_data = get_data("system_audit_log") or {}
            if log_id in audit_data:
                audit_data[log_id]['status'] = 'completed'
                audit_data[log_id]['completed_at'] = datetime.now().isoformat()
                audit_data[log_id]['results'] = {
                    'backup_id': backup_id,
                    'records_cleared': total_cleared,
                    'earnings_reset': earnings_reset_count
                }
                set_data("system_audit_log", audit_data)

        logger.info(f"Seasonal reset completed successfully by user {user_id}")

    except Exception as e:
        logger.error(f"Error executing seasonal reset: {e}")
        management_bot.send_message(
            message.chat.id,
            f"❌ **Reset Failed**\n\nError during seasonal reset: {str(e)}\n\nSome data may have been affected. Please contact support immediately."
        )

# ============================================================================
# DAILY ORDER CLEANUP FEATURE
# ============================================================================

def initiate_daily_cleanup(call):
    """Initiate daily order cleanup with options"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "🧹 Loading cleanup options...")

        # Analyze current order status
        confirmed_orders = get_data("confirmed_orders") or {}
        assignments = get_data("delivery_personnel_assignments") or {}

        current_time = datetime.now()

        # Count different types of orders to clean
        stale_orders = 0
        failed_assignments = 0
        abandoned_orders = 0

        for order_id, order in confirmed_orders.items():
            confirmed_at = order.get('confirmed_at', '')
            if confirmed_at:
                try:
                    order_time = datetime.fromisoformat(confirmed_at.replace('Z', '+00:00'))
                    hours_old = (current_time - order_time).total_seconds() / 3600

                    if hours_old > 24:
                        stale_orders += 1
                    elif hours_old > 2:
                        abandoned_orders += 1

                except:
                    continue

        # Count failed assignments
        for assignment_id, assignment in assignments.items():
            if assignment.get('status') in ['failed', 'timeout', 'cancelled']:
                failed_assignments += 1

        text = f"""🧹 **Daily Order Cleanup**

**Current System Status:**

📊 **Orders to Clean:**
• Stale orders (>24h): {stale_orders}
• Abandoned orders (>2h): {abandoned_orders}
• Failed assignments: {failed_assignments}
• Total confirmed orders: {len(confirmed_orders)}

**🔧 Cleanup Options:**

**🕐 Standard Cleanup (24h)**
• Remove orders older than 24 hours
• Archive incomplete deliveries
• Clean failed assignments
• Preserve current day operations

**⚡ Quick Cleanup (2h)**
• Remove orders older than 2 hours
• More aggressive cleanup
• For end-of-day operations

**🎯 Custom Cleanup**
• Choose specific time threshold
• Select order types to clean
• Advanced options

**📋 What gets preserved:**
• Completed orders (moved to completed_orders)
• Current active deliveries
• Personnel profiles and status
• System configuration"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🕐 Standard (24h)", callback_data="cleanup_standard"),
            types.InlineKeyboardButton("⚡ Quick (2h)", callback_data="cleanup_quick")
        )
        keyboard.add(
            types.InlineKeyboardButton("🎯 Custom Options", callback_data="cleanup_custom"),
            types.InlineKeyboardButton("📊 Order Status", callback_data="order_status")
        )
        keyboard.add(
            types.InlineKeyboardButton("❌ Cancel", callback_data="system_management")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error initiating daily cleanup: {e}")
        handle_analytics_error(call, str(e), "daily_cleanup_init")

def execute_standard_cleanup(call):
    """Execute standard 24-hour cleanup"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "🧹 Starting standard cleanup...")

        # Log the operation
        log_id = log_reset_operation("daily_cleanup_standard", user_id, {
            'cleanup_type': 'standard_24h',
            'initiated_at': datetime.now().isoformat()
        })

        text = "🧹 **Standard Cleanup (24h) - Starting...**\n\n⏳ Please wait while orders are processed..."

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(types.InlineKeyboardButton("🔄 Refresh Status", callback_data="cleanup_status"))

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error", show_alert=True)
            return

        # Execute cleanup with enhanced verification
        logger.info("🧹 Starting standard cleanup (24-hour threshold)")
        archived_count = archive_incomplete_orders(hours_threshold=24)

        # Clean failed assignments with verification
        assignments = get_data("delivery_personnel_assignments") or {}
        initial_assignment_count = len(assignments)
        failed_assignments = []

        for assignment_id, assignment in assignments.items():
            if assignment.get('status') in ['failed', 'timeout', 'cancelled']:
                failed_assignments.append(assignment_id)
                logger.info(f"🗑️ Marking failed assignment for removal: {assignment_id} (status: {assignment.get('status')})")

        # Remove failed assignments
        updated_assignments = assignments.copy()
        for assignment_id in failed_assignments:
            if assignment_id in updated_assignments:
                del updated_assignments[assignment_id]

        # Use safe operation for assignments
        assignments_success = safe_cleanup_operation(
            "delivery_personnel_assignments",
            updated_assignments,
            "Failed Assignments Cleanup"
        )

        if not assignments_success:
            logger.error("❌ Failed to clean assignments - using original data")
            updated_assignments = assignments

        # Update personnel availability with verification
        personnel_data = get_data("delivery_personnel") or {}
        personnel_updates = 0

        for personnel_id, person in personnel_data.items():
            # Reset status if they had failed assignments
            if person.get('status') == 'busy':
                # Check if they have any active assignments
                active_assignments = [
                    a for a in updated_assignments.values()
                    if a.get('personnel_id') == personnel_id and a.get('status') in ['assigned', 'picked_up']
                ]
                if not active_assignments:
                    person['status'] = 'available'
                    personnel_updates += 1
                    logger.info(f"👤 Updated personnel {personnel_id} status to available")

        # Use safe operation for personnel data
        personnel_success = safe_cleanup_operation(
            "delivery_personnel",
            personnel_data,
            "Personnel Status Update"
        )

        if not personnel_success:
            logger.error("❌ Failed to update personnel status")

        # Verify final state
        final_assignments = get_data("delivery_personnel_assignments") or {}
        final_assignment_count = len(final_assignments)

        logger.info(f"✅ Standard cleanup verification:")
        logger.info(f"   📦 Orders archived: {archived_count}")
        logger.info(f"   🗑️ Failed assignments removed: {len(failed_assignments)}")
        logger.info(f"   📊 Assignments: {initial_assignment_count} → {final_assignment_count}")
        logger.info(f"   👤 Personnel status updated: {personnel_updates}")

        # Completion report with safe MarkdownV2 formatting
        completion_template = """✅ **Standard Cleanup Completed**

**📊 Cleanup Results:**
• Archived orders: {archived_count}
• Failed assignments removed: {failed_assignments_count}
• Personnel status updated
• System optimized

**🕐 Cleanup Threshold:** 24 hours
**📅 Completed:** {completion_time}

**✨ System Status:**
• Stale orders removed
• Failed deliveries cleared
• Personnel availability updated
• Ready for continued operations

The system has been cleaned and optimized\\! 🚀"""

        # Safely format the completion message
        completion_text, format_success = safe_format_message(
            completion_template,
            archived_count=str(archived_count),
            failed_assignments_count=str(len(failed_assignments)),
            completion_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 View Orders", callback_data="order_status"),
            types.InlineKeyboardButton("👥 Check Personnel", callback_data="mgmt_personnel")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 System Management", callback_data="system_management")
        )

        # Use safe message editing with fallback mechanisms
        try:
            if format_success:
                # Try safe edit with MarkdownV2
                safe_edit_message_with_fallback(
                    management_bot,
                    call.message.chat.id,
                    call.message.message_id,
                    completion_text,
                    reply_markup=keyboard,
                    parse_mode='MarkdownV2'
                )
            else:
                # Fallback to plain text if formatting failed
                safe_edit_message_with_fallback(
                    management_bot,
                    call.message.chat.id,
                    call.message.message_id,
                    completion_text,
                    reply_markup=keyboard,
                    parse_mode=None
                )
        except Exception as msg_error:
            logger.error(f"Failed to send cleanup completion message: {msg_error}")
            # Final fallback - simple success message
            try:
                management_bot.edit_message_text(
                    f"✅ Standard Cleanup Completed\n\nArchived: {archived_count} orders\nRemoved: {len(failed_assignments)} assignments",
                    call.message.chat.id,
                    call.message.message_id,
                    reply_markup=keyboard,
                    parse_mode=None
                )
            except Exception as final_error:
                logger.error(f"Final fallback message also failed: {final_error}")

        # Update audit log
        if log_id:
            audit_data = get_data("system_audit_log") or {}
            if log_id in audit_data:
                audit_data[log_id]['status'] = 'completed'
                audit_data[log_id]['completed_at'] = datetime.now().isoformat()
                audit_data[log_id]['results'] = {
                    'archived_orders': archived_count,
                    'failed_assignments_removed': len(failed_assignments)
                }
                set_data("system_audit_log", audit_data)

        logger.info(f"Standard cleanup completed: {archived_count} orders archived, {len(failed_assignments)} assignments removed")

    except Exception as e:
        logger.error(f"Error executing standard cleanup: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Cleanup failed: {str(e)}", show_alert=True)

def execute_quick_cleanup(call):
    """Execute quick 2-hour cleanup"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "⚡ Starting quick cleanup...")

        # Log the operation
        log_id = log_reset_operation("daily_cleanup_quick", user_id, {
            'cleanup_type': 'quick_2h',
            'initiated_at': datetime.now().isoformat()
        })

        # Execute more aggressive cleanup
        archived_count = archive_incomplete_orders(hours_threshold=2)

        # Also clean assignments older than 2 hours
        assignments = get_data("delivery_personnel_assignments") or {}
        current_time = datetime.now()
        old_assignments = []

        for assignment_id, assignment in assignments.items():
            assigned_at = assignment.get('assigned_at', '')
            if assigned_at:
                try:
                    assignment_time = datetime.fromisoformat(assigned_at.replace('Z', '+00:00'))
                    hours_old = (current_time - assignment_time).total_seconds() / 3600

                    if hours_old > 2 and assignment.get('status') != 'delivered':
                        old_assignments.append(assignment_id)
                except:
                    continue

        # Remove old assignments
        for assignment_id in old_assignments:
            del assignments[assignment_id]

        set_data("delivery_personnel_assignments", assignments)

        # Update personnel availability
        personnel_data = get_data("delivery_personnel") or {}
        for personnel_id, person in personnel_data.items():
            if person.get('status') == 'busy':
                # Check if they have any active assignments
                active_assignments = [
                    a for a in assignments.values()
                    if a.get('personnel_id') == personnel_id and a.get('status') in ['assigned', 'picked_up']
                ]
                if not active_assignments:
                    person['status'] = 'available'

        set_data("delivery_personnel", personnel_data)

        # Quick cleanup completion message with safe formatting
        quick_completion_template = """⚡ **Quick Cleanup Completed**

**📊 Cleanup Results:**
• Archived orders: {archived_count}
• Old assignments removed: {old_assignments_count}
• Personnel status updated
• Aggressive cleanup performed

**⚡ Cleanup Threshold:** 2 hours
**📅 Completed:** {completion_time}

**✨ System Status:**
• Recent stale orders removed
• Old assignments cleared
• Personnel freed up
• System optimized for new orders

Quick cleanup completed\\! Ready for fresh operations\\. 🚀"""

        # Safely format the quick completion message
        completion_text, format_success = safe_format_message(
            quick_completion_template,
            archived_count=str(archived_count),
            old_assignments_count=str(len(old_assignments)),
            completion_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 View Orders", callback_data="order_status"),
            types.InlineKeyboardButton("👥 Check Personnel", callback_data="mgmt_personnel")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 System Management", callback_data="system_management")
        )

        # Use safe message editing for quick cleanup completion
        try:
            if format_success:
                safe_edit_message_with_fallback(
                    management_bot,
                    call.message.chat.id,
                    call.message.message_id,
                    completion_text,
                    reply_markup=keyboard,
                    parse_mode='MarkdownV2'
                )
            else:
                safe_edit_message_with_fallback(
                    management_bot,
                    call.message.chat.id,
                    call.message.message_id,
                    completion_text,
                    reply_markup=keyboard,
                    parse_mode=None
                )
        except Exception as msg_error:
            logger.error(f"Failed to send quick cleanup completion message: {msg_error}")
            try:
                management_bot.edit_message_text(
                    f"⚡ Quick Cleanup Completed\n\nArchived: {archived_count} orders\nRemoved: {len(old_assignments)} assignments",
                    call.message.chat.id,
                    call.message.message_id,
                    reply_markup=keyboard,
                    parse_mode=None
                )
            except Exception as final_error:
                logger.error(f"Quick cleanup final fallback message failed: {final_error}")

        # Update audit log
        if log_id:
            audit_data = get_data("system_audit_log") or {}
            if log_id in audit_data:
                audit_data[log_id]['status'] = 'completed'
                audit_data[log_id]['completed_at'] = datetime.now().isoformat()
                audit_data[log_id]['results'] = {
                    'archived_orders': archived_count,
                    'old_assignments_removed': len(old_assignments)
                }
                set_data("system_audit_log", audit_data)

        logger.info(f"Quick cleanup completed: {archived_count} orders archived, {len(old_assignments)} assignments removed")

    except Exception as e:
        logger.error(f"Error executing quick cleanup: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Cleanup failed: {str(e)}", show_alert=True)

# ============================================================================
# THREE-CATEGORY ORDER ANALYTICS SYSTEM
# ============================================================================

def categorize_orders_by_status(completed_orders, confirmed_orders, assignments_data):
    """Categorize orders into Complete, Incomplete, and Issue Reported

    Updated logic:
    - Orders with issues remain in incomplete count during re-confirmation process
    - Only move to complete when they reach final completion status
    - Track issues separately but don't remove from incomplete until resolved
    """
    try:
        complete_orders = []
        incomplete_orders = []
        issue_reported_orders = []

        # Process completed orders (orders that have been moved to completed_orders collection)
        for order_id, order in completed_orders.items():
            # Orders in completed_orders collection are by definition complete
            # Check for issue reports for tracking purposes
            delivery_status = order.get('delivery_status', '')
            issue_reported_at = order.get('issue_reported_at')
            confirmation_count = order.get('confirmation_attempts', 0)

            # Track if this order had issues (for statistics)
            has_issue = delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2

            # All orders in completed_orders are complete regardless of issue history
            complete_orders.append(order)

            # Also add to issue_reported for tracking if it had issues
            if has_issue:
                issue_reported_orders.append(order)

        # Process confirmed orders (orders still in progress)
        for order_id, order in confirmed_orders.items():
            delivery_status = order.get('delivery_status', '')
            issue_reported_at = order.get('issue_reported_at')
            confirmation_count = order.get('confirmation_attempts', 0)

            # Check if order is truly completed (customer confirmed)
            # Only orders with 'customer_confirmed' status should be counted as complete
            # Orders with 'completed' status are only delivery-completed, not customer-confirmed
            if delivery_status == 'customer_confirmed':
                complete_orders.append(order)
                # Also track if it had issues
                if delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2:
                    issue_reported_orders.append(order)
            else:
                # All other orders are incomplete (including delivery-completed but not customer-confirmed)
                # This includes orders with delivery_status 'completed' that haven't been customer-confirmed
                # Orders with issues stay in incomplete count during re-confirmation process
                incomplete_orders.append(order)

                # Track issues separately for statistics
                if delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2:
                    issue_reported_orders.append(order)

        return {
            'complete': complete_orders,
            'incomplete': incomplete_orders,
            'issue_reported': issue_reported_orders
        }

    except Exception as e:
        logger.error(f"Error categorizing orders: {e}")
        return {
            'complete': [],
            'incomplete': [],
            'issue_reported': []
        }

def calculate_category_percentages(categorized_orders):
    """Calculate percentages for each order category

    Updated logic:
    - Total count is based on complete + incomplete (no double counting)
    - Issue count is tracked separately for statistics
    - Orders with issues are counted in incomplete until resolved
    """
    try:
        complete_count = len(categorized_orders['complete'])
        incomplete_count = len(categorized_orders['incomplete'])
        issue_count = len(categorized_orders['issue_reported'])

        # Total count is complete + incomplete (no double counting since issues stay in incomplete)
        total_count = complete_count + incomplete_count

        if total_count == 0:
            return {
                'complete_percentage': 0,
                'incomplete_percentage': 0,
                'issue_percentage': 0,
                'complete_pct': 0,
                'incomplete_pct': 0,
                'issue_pct': 0,
                'total_count': 0,
                'complete_count': 0,
                'incomplete_count': 0,
                'issue_count': 0
            }

        return {
            'complete_percentage': (complete_count / total_count) * 100,
            'incomplete_percentage': (incomplete_count / total_count) * 100,
            'issue_percentage': (issue_count / total_count) * 100 if total_count > 0 else 0,
            'complete_pct': (complete_count / total_count) * 100,
            'incomplete_pct': (incomplete_count / total_count) * 100,
            'issue_pct': (issue_count / total_count) * 100 if total_count > 0 else 0,
            'total_count': total_count,
            'complete_count': complete_count,
            'incomplete_count': incomplete_count,
            'issue_count': issue_count
        }

    except Exception as e:
        logger.error(f"Error calculating category percentages: {e}")
        return {
            'complete_percentage': 0,
            'incomplete_percentage': 0,
            'issue_percentage': 0,
            'complete_pct': 0,
            'incomplete_pct': 0,
            'issue_pct': 0,
            'total_count': 0,
            'complete_count': 0,
            'incomplete_count': 0,
            'issue_count': 0
        }

def analyze_personnel_performance_by_categories(categorized_orders, assignments_data, personnel_data):
    """Analyze individual personnel performance across order categories"""
    try:
        personnel_performance = {}

        # Initialize performance tracking for each personnel
        for personnel_id, person in personnel_data.items():
            personnel_performance[personnel_id] = {
                'name': person.get('name', 'Unknown'),
                'complete_orders': 0,
                'incomplete_orders': 0,
                'issue_orders': 0,
                'total_assignments': 0,
                'completion_rate': 0,
                'issue_rate': 0
            }

        # Count assignments by personnel
        for assignment_id, assignment in assignments_data.items():
            personnel_id = assignment.get('personnel_id')
            if personnel_id and personnel_id in personnel_performance:
                personnel_performance[personnel_id]['total_assignments'] += 1

        # Analyze completed orders by personnel
        for order in categorized_orders['complete']:
            # Find assignment for this order
            order_number = order.get('order_number')
            for assignment in assignments_data.values():
                if assignment.get('order_number') == order_number:
                    personnel_id = assignment.get('personnel_id')
                    if personnel_id and personnel_id in personnel_performance:
                        personnel_performance[personnel_id]['complete_orders'] += 1
                    break

        # Analyze incomplete orders by personnel
        for order in categorized_orders['incomplete']:
            order_number = order.get('order_number')
            for assignment in assignments_data.values():
                if assignment.get('order_number') == order_number:
                    personnel_id = assignment.get('personnel_id')
                    if personnel_id and personnel_id in personnel_performance:
                        personnel_performance[personnel_id]['incomplete_orders'] += 1
                    break

        # Analyze issue reported orders by personnel
        for order in categorized_orders['issue_reported']:
            order_number = order.get('order_number')
            for assignment in assignments_data.values():
                if assignment.get('order_number') == order_number:
                    personnel_id = assignment.get('personnel_id')
                    if personnel_id and personnel_id in personnel_performance:
                        personnel_performance[personnel_id]['issue_orders'] += 1
                    break

        # Calculate rates
        for personnel_id, performance in personnel_performance.items():
            total = performance['complete_orders'] + performance['incomplete_orders'] + performance['issue_orders']
            if total > 0:
                performance['completion_rate'] = (performance['complete_orders'] / total) * 100
                performance['issue_rate'] = (performance['issue_orders'] / total) * 100
            else:
                performance['completion_rate'] = 0
                performance['issue_rate'] = 0

        return personnel_performance

    except Exception as e:
        logger.error(f"Error analyzing personnel performance: {e}")
        return {}

# ============================================================================
# DYNAMIC DELIVERY PERSONNEL AUTHORIZATION SYSTEM
# ============================================================================

def get_authorized_delivery_personnel():
    """Get all authorized delivery personnel from Firestore"""
    try:
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        return authorized_personnel
    except Exception as e:
        logger.error(f"Error getting authorized delivery personnel: {e}")
        return {}

def get_authorized_delivery_ids():
    """Get list of authorized Telegram IDs for delivery bot access"""
    try:
        authorized_personnel = get_authorized_delivery_personnel()
        authorized_ids = []

        for personnel_id, person_data in authorized_personnel.items():
            if person_data.get('status') == 'active':
                telegram_id = person_data.get('telegram_id')
                if telegram_id:
                    authorized_ids.append(int(telegram_id))

        # Always include admin IDs as fallback
        from src.config import DELIVERY_BOT_AUTHORIZED_IDS, SYSTEM_ADMIN_ID
        admin_ids = DELIVERY_BOT_AUTHORIZED_IDS if 'DELIVERY_BOT_AUTHORIZED_IDS' in globals() else [SYSTEM_ADMIN_ID]

        # Combine and deduplicate
        all_ids = list(set(authorized_ids + admin_ids))
        logger.info(f"Retrieved {len(all_ids)} authorized delivery personnel IDs")

        return all_ids

    except Exception as e:
        logger.error(f"Error getting authorized delivery IDs: {e}")
        # Fallback to config file
        try:
            from src.config import DELIVERY_BOT_AUTHORIZED_IDS
            return DELIVERY_BOT_AUTHORIZED_IDS
        except:
            from src.config import SYSTEM_ADMIN_ID
            return [SYSTEM_ADMIN_ID]  # Ultimate fallback

def add_authorized_delivery_personnel(telegram_id, name, added_by_admin_id):
    """Add new personnel to authorized delivery list in Firestore"""
    try:
        # Validate Telegram ID
        if not isinstance(telegram_id, (int, str)) or not str(telegram_id).isdigit():
            raise ValueError(f"Invalid Telegram ID: {telegram_id}")

        telegram_id = int(telegram_id)

        # Get current authorized personnel
        authorized_personnel = get_authorized_delivery_personnel()

        # Create unique personnel ID
        personnel_id = f"delivery_personnel_{telegram_id}"

        # Create personnel record
        personnel_record = {
            'telegram_id': telegram_id,
            'name': name,
            'status': 'active',
            'added_date': safe_get_current_timestamp(),
            'added_by': str(added_by_admin_id)
        }

        # Add to authorized personnel
        authorized_personnel[personnel_id] = personnel_record

        # Save to Firestore
        set_data("authorized_delivery_personnel", authorized_personnel)

        # Log the authorization change
        log_authorization_change("add", telegram_id, name, added_by_admin_id)

        logger.info(f"Added authorized delivery personnel: {name} (ID: {telegram_id})")
        return True

    except Exception as e:
        logger.error(f"Error adding authorized delivery personnel: {e}")
        return False

def remove_authorized_delivery_personnel(telegram_id, removed_by_admin_id):
    """Remove personnel from authorized delivery list in Firestore"""
    try:
        telegram_id = int(telegram_id)

        # Get current authorized personnel
        authorized_personnel = get_authorized_delivery_personnel()

        # Find and remove personnel record
        personnel_id = f"delivery_personnel_{telegram_id}"
        removed_name = "Unknown"

        if personnel_id in authorized_personnel:
            removed_name = authorized_personnel[personnel_id].get('name', 'Unknown')
            # Mark as inactive instead of deleting for audit trail
            authorized_personnel[personnel_id]['status'] = 'inactive'
            authorized_personnel[personnel_id]['removed_date'] = safe_get_current_timestamp()
            authorized_personnel[personnel_id]['removed_by'] = str(removed_by_admin_id)
        else:
            # If not found by personnel_id, search by telegram_id
            for pid, person_data in authorized_personnel.items():
                if person_data.get('telegram_id') == telegram_id:
                    removed_name = person_data.get('name', 'Unknown')
                    authorized_personnel[pid]['status'] = 'inactive'
                    authorized_personnel[pid]['removed_date'] = safe_get_current_timestamp()
                    authorized_personnel[pid]['removed_by'] = str(removed_by_admin_id)
                    break

        # Save to Firestore
        set_data("authorized_delivery_personnel", authorized_personnel)

        # Log the authorization change
        log_authorization_change("remove", telegram_id, removed_name, removed_by_admin_id)

        logger.info(f"Removed authorized delivery personnel: {removed_name} (ID: {telegram_id})")
        return True

    except Exception as e:
        logger.error(f"Error removing authorized delivery personnel: {e}")
        return False

def log_authorization_change(action, telegram_id, name, admin_id):
    """Log authorization changes for audit purposes"""
    try:
        audit_data = get_data("authorization_audit_log") or {}

        log_entry = {
            'timestamp': safe_get_current_timestamp(),
            'action': action,  # 'add' or 'remove'
            'telegram_id': int(telegram_id),
            'personnel_name': name,
            'admin_id': str(admin_id),
            'status': 'completed'
        }

        log_id = f"auth_{action}_{telegram_id}_{safe_get_current_timestamp().replace(':', '-').replace('.', '-')}"
        audit_data[log_id] = log_entry

        set_data("authorization_audit_log", audit_data)
        logger.info(f"Authorization change logged: {action} {name} (ID: {telegram_id})")

    except Exception as e:
        logger.error(f"Error logging authorization change: {e}")

def validate_telegram_id_with_result(telegram_id):
    """Validate that a Telegram ID is properly formatted and return tuple result"""
    try:
        # Convert to int and check if it's a valid Telegram ID
        tid = int(telegram_id)

        # Telegram IDs are typically 9-10 digits
        if 100000000 <= tid <= 9999999999:
            return True, tid
        else:
            return False, None

    except (ValueError, TypeError):
        return False, None

# ============================================================================
# COMPREHENSIVE ORDER LIMIT RESET SYSTEM
# ============================================================================

def get_personnel_order_summary(personnel_id):
    """Get comprehensive order summary for a specific personnel"""
    try:
        # Get personnel data
        personnel_data = get_data("delivery_personnel") or {}
        person = personnel_data.get(personnel_id, {})

        if not person:
            return None

        # Get assignments data
        assignments_data = get_data("delivery_personnel_assignments") or {}
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}

        # Count orders for this personnel
        active_assignments = []
        completed_assignments = []
        incomplete_assignments = []

        for assignment_id, assignment in assignments_data.items():
            if assignment.get('personnel_id') == personnel_id:
                status = assignment.get('status', '')
                if status in ['assigned', 'picked_up']:
                    active_assignments.append(assignment)
                elif status in ['delivered', 'completed']:
                    completed_assignments.append(assignment)
                else:
                    incomplete_assignments.append(assignment)

        # Calculate completion patterns
        total_assignments = len(active_assignments) + len(completed_assignments) + len(incomplete_assignments)
        completion_rate = (len(completed_assignments) / total_assignments * 100) if total_assignments > 0 else 0
        incomplete_rate = (len(incomplete_assignments) / total_assignments * 100) if total_assignments > 0 else 0

        return {
            'personnel_id': personnel_id,
            'name': person.get('name', 'Unknown'),
            'telegram_id': person.get('telegram_id', 'Unknown'),
            'status': person.get('status', 'offline'),
            'active_orders': len(active_assignments),
            'completed_orders': len(completed_assignments),
            'incomplete_orders': len(incomplete_assignments),
            'total_assignments': total_assignments,
            'completion_rate': completion_rate,
            'incomplete_rate': incomplete_rate,
            'active_assignments': active_assignments,
            'incomplete_assignments': incomplete_assignments
        }

    except Exception as e:
        logger.error(f"Error getting personnel order summary for {personnel_id}: {e}")
        return None

def reset_personnel_orders(personnel_id, reset_by_admin_id):
    """Reset all orders and assignments for a specific personnel"""
    try:
        # Get current data
        assignments_data = get_data("delivery_personnel_assignments") or {}
        personnel_data = get_data("delivery_personnel") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}

        # Find assignments for this personnel
        assignments_to_remove = []
        for assignment_id, assignment in assignments_data.items():
            if assignment.get('personnel_id') == personnel_id:
                assignments_to_remove.append(assignment_id)

        # Remove assignments
        for assignment_id in assignments_to_remove:
            del assignments_data[assignment_id]

        # Reset personnel status and capacity
        if personnel_id in personnel_data:
            personnel_data[personnel_id]['status'] = 'available'
            personnel_data[personnel_id]['current_orders'] = 0

        if personnel_id in capacity_data:
            capacity_data[personnel_id] = 0

        # Clear capacity tracking data for this personnel
        logger.info(f"🔄 Clearing capacity tracking data for personnel {personnel_id}...")
        try:
            delete_data(f"delivery_personnel_capacity_tracking/{personnel_id}")
            logger.info(f"✅ Cleared capacity tracking for {personnel_id}")
        except Exception as e:
            logger.error(f"Failed to clear capacity tracking for {personnel_id}: {e}")

        # Save all changes
        set_data("delivery_personnel_assignments", assignments_data)
        set_data("delivery_personnel", personnel_data)
        set_data("delivery_personnel_capacity", capacity_data)

        # Force refresh personnel eligibility after reset
        try:
            from src.utils.delivery_personnel_utils import trigger_immediate_broadcast_eligibility_update
            is_eligible = trigger_immediate_broadcast_eligibility_update(personnel_id)
            logger.info(f"Personnel {personnel_id} eligibility after reset: {is_eligible}")
        except Exception as e:
            logger.error(f"Failed to refresh eligibility for {personnel_id}: {e}")

        # Log the reset operation
        log_reset_operation(f"personnel_reset_{personnel_id}", reset_by_admin_id, {
            'personnel_id': personnel_id,
            'assignments_removed': len(assignments_to_remove),
            'reset_type': 'individual'
        })

        logger.info(f"Reset orders for personnel {personnel_id}: {len(assignments_to_remove)} assignments removed")
        return len(assignments_to_remove)

    except Exception as e:
        logger.error(f"Error resetting personnel orders for {personnel_id}: {e}")
        return 0

def reset_all_personnel_orders(reset_by_admin_id):
    """Reset orders for all delivery personnel (global reset) - includes order data cleanup"""
    try:
        # Get current data
        assignments_data = get_data("delivery_personnel_assignments") or {}
        personnel_data = get_data("delivery_personnel") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        completed_orders = get_data("completed_orders") or {}

        total_assignments_removed = len(assignments_data)
        personnel_reset_count = 0
        confirmed_orders_cleared = len(confirmed_orders)
        completed_orders_cleared = len(completed_orders)

        # Clear all assignments
        assignments_data = {}

        # Reset all personnel status and capacity
        for personnel_id, person in personnel_data.items():
            person['status'] = 'available'
            person['current_orders'] = 0
            personnel_reset_count += 1

        # Reset all capacity data
        for personnel_id in capacity_data:
            capacity_data[personnel_id] = 0

        # Clear capacity tracking data for all personnel
        logger.info(f"🔄 Clearing capacity tracking data for all personnel...")
        try:
            # Clear the entire capacity tracking collection
            set_data("delivery_personnel_capacity_tracking", {})
            logger.info(f"✅ Cleared delivery_personnel_capacity_tracking collection")
        except Exception as e:
            logger.error(f"Failed to clear capacity tracking data: {e}")

        # Clear order data for complete reset
        confirmed_orders = {}
        completed_orders = {}

        # Save all changes to Firebase using safe operations
        logger.info(f"🔄 Starting global reset Firebase operations...")
        success_count = 0
        operations = [
            ("delivery_personnel_assignments", {}),
            ("delivery_personnel", personnel_data),
            ("delivery_personnel_capacity", capacity_data),
            ("confirmed_orders", {}),
            ("completed_orders", {}),
            ("order_broadcast_messages", {}),
            ("delivery_personnel_capacity_tracking", {}),
            ("analytics_counters", {}),  # Clear analytics counters to fix monthly analytics
            ("user_payment_messages", {}),  # Clear customer confirmation button data
            ("time_based_reset_tracking", {}),  # Clear time-based reset tracking
            ("analytics_counter_backups", {}),  # Clear analytics counter backups
            ("order_broadcast_metadata", {}),  # Clear order broadcast metadata
            ("pending_admin_reviews", {}),  # Clear pending admin reviews
            ("admin_remarks", {}),  # Clear admin remarks
            ("awaiting_receipt", {}),  # Clear awaiting receipt data
            ("order_status", {}),  # Clear order status data
            ("current_orders", {}),  # Clear current orders being created
            ("customer_delivery_messages", {}),  # Clear customer delivery completion messages

        ]

        for collection_name, data in operations:
            logger.info(f"🔄 Processing {collection_name}...")
            if safe_firebase_set_with_verification(collection_name, data):
                success_count += 1
                logger.info(f"✅ Successfully processed {collection_name}")
            else:
                logger.error(f"❌ Failed to reset {collection_name}")

        # Verify all operations succeeded
        if success_count != len(operations):
            logger.error(f"❌ Global reset partially failed: {success_count}/{len(operations)} operations succeeded")
            return {'assignments_removed': 0, 'personnel_reset': 0, 'confirmed_orders_cleared': 0, 'completed_orders_cleared': 0}

        logger.info(f"✅ All {len(operations)} Firebase operations completed successfully")

        # Complete personnel data wipe for global reset
        try:
            from src.utils.delivery_personnel_utils import complete_personnel_data_wipe
            wipe_success = complete_personnel_data_wipe()
            if wipe_success:
                logger.info("✅ Complete personnel data wipe successful")
            else:
                logger.warning("⚠️ Some issues occurred during personnel data wipe")
        except Exception as e:
            logger.error(f"Failed to wipe personnel data: {e}")

        # Force refresh all personnel eligibility after global reset
        try:
            from src.utils.delivery_personnel_utils import reset_all_personnel_capacity_and_eligibility
            eligibility_reset_success = reset_all_personnel_capacity_and_eligibility()
            if eligibility_reset_success:
                logger.info("✅ All personnel capacity and eligibility reset successfully")
            else:
                logger.warning("⚠️ Some issues occurred during personnel eligibility reset")
        except Exception as e:
            logger.error(f"Failed to reset personnel eligibility: {e}")

        # Log the reset operation
        log_reset_operation("global_order_reset", reset_by_admin_id, {
            'assignments_removed': total_assignments_removed,
            'personnel_reset': personnel_reset_count,
            'confirmed_orders_cleared': confirmed_orders_cleared,
            'completed_orders_cleared': completed_orders_cleared,
            'reset_type': 'global_complete'
        })

        logger.info(f"Global order reset: {total_assignments_removed} assignments, {confirmed_orders_cleared} confirmed orders, {completed_orders_cleared} completed orders cleared, {personnel_reset_count} personnel reset")
        return {
            'assignments_removed': total_assignments_removed,
            'personnel_reset': personnel_reset_count,
            'confirmed_orders_cleared': confirmed_orders_cleared,
            'completed_orders_cleared': completed_orders_cleared
        }

    except Exception as e:
        logger.error(f"Error in global order reset: {e}")
        return {'assignments_removed': 0, 'personnel_reset': 0, 'confirmed_orders_cleared': 0, 'completed_orders_cleared': 0}

def show_order_reset_menu(call):
    """Show order reset management menu"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "🔄 Loading order reset options...")

        # Get current system status
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        total_personnel = len(personnel_data)
        total_assignments = len(assignments_data)

        # Count personnel with orders
        personnel_with_orders = 0
        for personnel_id in personnel_data:
            has_orders = any(
                assignment.get('personnel_id') == personnel_id
                for assignment in assignments_data.values()
            )
            if has_orders:
                personnel_with_orders += 1

        text = f"""🔄 **Order Reset Management**

**Current System Status:**
• Total Personnel: {total_personnel}
• Active Assignments: {total_assignments}
• Personnel with Orders: {personnel_with_orders}

**Reset Options:**

**🌐 Global Reset**
• Reset ALL personnel orders
• Clear ALL active assignments
• Set ALL personnel to available
• Use for system-wide maintenance

**👤 Individual Reset**
• Select specific personnel
• View their order details
• Reset only their assignments
• Preserve other personnel orders

**⚠️ Warning:** Reset operations cannot be undone. All active assignments will be cleared."""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🌐 Global Reset", callback_data="order_reset_global"),
            types.InlineKeyboardButton("👤 Individual Reset", callback_data="order_reset_individual")
        )
        keyboard.add(
            types.InlineKeyboardButton("📊 View Status", callback_data="order_reset_status"),
            types.InlineKeyboardButton("🔙 Back", callback_data="system_management")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error showing order reset menu: {e}")
        handle_analytics_error(call, str(e), "order_reset_menu")

def initiate_global_order_reset(call):
    """Initiate global order reset with warning"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "⚠️ Global reset initiated...")

        # Get current system status
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        completed_orders = get_data("completed_orders") or {}

        total_personnel = len(personnel_data)
        total_assignments = len(assignments_data)

        text = f"""⚠️ **GLOBAL ORDER RESET WARNING**

**This operation will PERMANENTLY clear:**

🔄 **Personnel Data:**
• Clear {total_assignments} active assignments
• Reset {total_personnel} personnel to available status
• Clear all order capacities to zero

📊 **Order Data:**
• Clear {len(confirmed_orders)} confirmed orders
• Clear {len(completed_orders)} completed orders
• Remove all order history and analytics
• Clear order broadcast messages

**⚠️ CRITICAL WARNING:**
• ALL order data and assignments will be lost
• Personnel will be reset to available status
• Order history and analytics will be cleared
• This action CANNOT be undone

**Use Cases:**
• System maintenance
• End of day cleanup
• Emergency reset
• Fresh start operations

**Type "CONFIRM GLOBAL RESET" to proceed**"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("⚠️ I Understand - Continue", callback_data="confirm_global_reset"),
            types.InlineKeyboardButton("❌ Cancel", callback_data="order_reset_menu")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error initiating global order reset: {e}")
        handle_analytics_error(call, str(e), "global_order_reset")

def execute_global_order_reset(call):
    """Execute global order reset"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "🔄 Executing global reset...")

        # Execute the reset
        reset_results = reset_all_personnel_orders(user_id)

        completion_text = f"""✅ **Global Order Reset Completed**

**📊 Reset Results:**
• Assignments Cleared: {reset_results['assignments_removed']}
• Personnel Reset: {reset_results['personnel_reset']}
• Confirmed Orders Cleared: {reset_results.get('confirmed_orders_cleared', 0)}
• Completed Orders Cleared: {reset_results.get('completed_orders_cleared', 0)}
• All personnel set to available
• All capacities reset to zero

**📅 Completed:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**✨ System Status:**
• All personnel available for new orders
• Order assignment system ready
• Delivery capacity optimized
• Order history cleared
• System ready for fresh operations

The global order reset has been completed successfully! 🚀"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 View Status", callback_data="order_reset_status"),
            types.InlineKeyboardButton("👥 Check Personnel", callback_data="mgmt_personnel")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Reset Menu", callback_data="order_reset_menu")
        )

        if not safe_edit_message(call, completion_text, keyboard):
            management_bot.send_message(
                call.message.chat.id,
                completion_text,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

        logger.info(f"Global order reset completed by user {user_id}")

    except Exception as e:
        logger.error(f"Error executing global order reset: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Reset failed: {str(e)}", show_alert=True)

# ============================================================================
# TELEGRAM MESSAGE FORMATTING AND VALIDATION
# ============================================================================

def escape_markdown_v2(text):
    """Escape special characters for Telegram MarkdownV2 format"""
    if not text:
        return ""

    # Convert to string if not already
    text = str(text)

    # Characters that need escaping in MarkdownV2
    # NOTE: Removed '.' from escape_chars to fix decimal number display
    escape_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '!']

    for char in escape_chars:
        text = text.replace(char, f'\\{char}')

    return text

def safe_format_message(template, **kwargs):
    """Safely format message with escaped Markdown content"""
    try:
        # Escape all dynamic content
        escaped_kwargs = {}
        for key, value in kwargs.items():
            if isinstance(value, str):
                escaped_kwargs[key] = escape_markdown_v2(value)
            else:
                escaped_kwargs[key] = escape_markdown_v2(str(value))

        # Format the message
        formatted_message = template.format(**escaped_kwargs)

        # Validate message length (Telegram limit: 4096 characters)
        if len(formatted_message) > 4096:
            logger.warning(f"Message too long: {len(formatted_message)} characters")
            # Truncate and add warning
            formatted_message = formatted_message[:4000] + "\n\n⚠️ Message truncated due to length limit"

        return formatted_message, True

    except Exception as e:
        logger.error(f"Error formatting message: {e}")
        return "✅ Operation completed successfully. Details available in logs.", False

def safe_send_message(bot, chat_id, text, reply_markup=None, parse_mode='MarkdownV2'):
    """Safely send message with fallback to plain text"""
    try:
        # First try with MarkdownV2
        return bot.send_message(
            chat_id,
            text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
    except Exception as e:
        logger.warning(f"MarkdownV2 message failed: {e}")
        try:
            # Fallback to plain text
            plain_text = text.replace('\\', '').replace('*', '').replace('_', '').replace('`', '')
            return bot.send_message(
                chat_id,
                plain_text,
                reply_markup=reply_markup,
                parse_mode=None
            )
        except Exception as e2:
            logger.error(f"Plain text message also failed: {e2}")
            return None

def safe_edit_message_with_fallback(bot, chat_id, message_id, text, reply_markup=None, parse_mode='MarkdownV2'):
    """Safely edit message with fallback options"""
    try:
        # First try with MarkdownV2
        return bot.edit_message_text(
            text,
            chat_id,
            message_id,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
    except Exception as e:
        logger.warning(f"MarkdownV2 edit failed: {e}")
        try:
            # Fallback to plain text edit
            plain_text = text.replace('\\', '').replace('*', '').replace('_', '').replace('`', '')
            return bot.edit_message_text(
                plain_text,
                chat_id,
                message_id,
                reply_markup=reply_markup,
                parse_mode=None
            )
        except Exception as e2:
            logger.warning(f"Plain text edit failed: {e2}")
            try:
                # Final fallback: send new message
                return safe_send_message(bot, chat_id, text, reply_markup, parse_mode)
            except Exception as e3:
                logger.error(f"All message methods failed: {e3}")
                return None

# ============================================================================
# SECURITY AND ACCESS CONTROL
# ============================================================================

def show_system_status(call):
    """Show current system status and health"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "📊 Loading system status...")

        # Get system data
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        personnel = get_data("delivery_personnel") or {}
        assignments = get_data("delivery_personnel_assignments") or {}

        # Calculate system health metrics
        total_orders = len(completed_orders) + len(confirmed_orders)
        active_personnel = len([p for p in personnel.values() if p.get('status') == 'available'])
        busy_personnel = len([p for p in personnel.values() if p.get('status') == 'busy'])

        # Check for stale orders
        current_time = datetime.now()
        stale_orders = 0
        for order in confirmed_orders.values():
            confirmed_at = order.get('confirmed_at', '')
            if confirmed_at:
                try:
                    order_time = datetime.fromisoformat(confirmed_at.replace('Z', '+00:00'))
                    hours_old = (current_time - order_time).total_seconds() / 3600
                    if hours_old > 24:
                        stale_orders += 1
                except:
                    continue

        # System health status
        health_status = "🟢 Healthy"
        if stale_orders > 5:
            health_status = "🟡 Needs Cleanup"
        elif stale_orders > 10:
            health_status = "🔴 Critical"

        # Calculate total revenue
        total_revenue = sum(
            safe_get_numeric_value(order, 'subtotal', 0) + safe_get_numeric_value(order, 'delivery_fee', 0)
            for order in completed_orders.values()
        )

        text = f"""📊 **System Status Dashboard**

**🏥 System Health:** {health_status}

**📈 Data Overview:**
• Total Orders: {total_orders}
• Completed: {len(completed_orders)}
• In Progress: {len(confirmed_orders)}
• Stale Orders (>24h): {stale_orders}

**👥 Personnel Status:**
• Total Staff: {len(personnel)}
• Available: {active_personnel}
• Busy: {busy_personnel}
• Offline: {len(personnel) - active_personnel - busy_personnel}

**💰 Financial Summary:**
• Total Revenue: {total_revenue:.0f} birr
• Active Assignments: {len(assignments)}

**🔧 System Operations:**
• Last Reset: Check audit log
• Data Collections: Active
• Backup Status: Available

**⚠️ Recommendations:**
{f"• Run daily cleanup ({stale_orders} stale orders)" if stale_orders > 0 else "• System running optimally"}
• Regular backups recommended
• Monitor personnel availability"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🧹 Run Cleanup", callback_data="reset_daily_init"),
            types.InlineKeyboardButton("📋 Audit Log", callback_data="audit_log")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="system_status"),
            types.InlineKeyboardButton("🔙 Back", callback_data="system_management")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error showing system status: {e}")
        handle_analytics_error(call, str(e), "system_status")

def show_audit_log(call):
    """Show system audit log"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "📋 Loading audit log...")

        audit_data = get_data("system_audit_log") or {}

        if not audit_data:
            text = """📋 **System Audit Log**

No audit entries found.

**Audit Log tracks:**
• Seasonal data resets
• Daily cleanup operations
• User access and authorization
• System modifications

All future operations will be logged here."""
        else:
            # Sort by timestamp (most recent first)
            sorted_entries = sorted(
                audit_data.items(),
                key=lambda x: x[1].get('timestamp', ''),
                reverse=True
            )

            # Show last 10 entries
            recent_entries = sorted_entries[:10]

            log_entries = []
            for log_id, entry in recent_entries:
                timestamp = entry.get('timestamp', 'Unknown')
                operation = entry.get('operation_type', 'Unknown')
                status = entry.get('status', 'Unknown')
                user_id_log = entry.get('user_id', 'Unknown')

                # Format timestamp
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    formatted_time = dt.strftime('%m/%d %H:%M')
                except:
                    formatted_time = timestamp[:16]

                status_emoji = "✅" if status == "completed" else "⏳" if status == "initiated" else "❌"

                log_entries.append(f"• {status_emoji} {formatted_time} - {operation} (User: {user_id_log})")

            log_text = '\n'.join(log_entries)

            text = f"""📋 **System Audit Log**

**Recent Operations (Last 10):**
{log_text}

**Legend:**
• ✅ Completed successfully
• ⏳ In progress/Initiated
• ❌ Failed or cancelled

**Total Entries:** {len(audit_data)}
**Showing:** Last {len(recent_entries)} entries"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="audit_log"),
            types.InlineKeyboardButton("📊 System Status", callback_data="system_status")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="system_management")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error showing audit log: {e}")
        handle_analytics_error(call, str(e), "audit_log")

def show_order_status(call):
    """Show current order status overview"""
    try:
        user_id = call.from_user.id
        if not is_authorized_for_reset(user_id):
            management_bot.answer_callback_query(call.id, "❌ Unauthorized access", show_alert=True)
            return

        management_bot.answer_callback_query(call.id, "📊 Loading order status...")

        # Get order data
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        assignments = get_data("delivery_personnel_assignments") or {}

        # Analyze orders by age
        current_time = datetime.now()
        today = current_time.strftime("%Y-%m-%d")

        today_completed = 0
        today_confirmed = 0
        stale_orders = []
        recent_orders = []

        for order_id, order in confirmed_orders.items():
            confirmed_at = order.get('confirmed_at', '')
            if confirmed_at:
                if confirmed_at.startswith(today):
                    today_confirmed += 1

                try:
                    order_time = datetime.fromisoformat(confirmed_at.replace('Z', '+00:00'))
                    hours_old = (current_time - order_time).total_seconds() / 3600

                    if hours_old > 24:
                        stale_orders.append({
                            'id': order_id,
                            'hours_old': hours_old,
                            'order_number': order.get('order_number', 'Unknown')
                        })
                    elif hours_old < 2:
                        recent_orders.append({
                            'id': order_id,
                            'hours_old': hours_old,
                            'order_number': order.get('order_number', 'Unknown')
                        })
                except:
                    continue

        for order_id, order in completed_orders.items():
            completed_at = order.get('completed_at', '')
            if completed_at and completed_at.startswith(today):
                today_completed += 1

        # Assignment status
        active_assignments = len([a for a in assignments.values() if a.get('status') == 'assigned'])
        failed_assignments = len([a for a in assignments.values() if a.get('status') in ['failed', 'timeout']])

        # Create status breakdown
        stale_list = '\n'.join([f"• #{order['order_number']} ({order['hours_old']:.1f}h old)" for order in stale_orders[:5]])
        if len(stale_orders) > 5:
            stale_list += f"\n... and {len(stale_orders) - 5} more"

        if not stale_list:
            stale_list = "• No stale orders found"

        text = f"""📊 **Order Status Overview**

**📅 Today's Activity:**
• Completed: {today_completed}
• In Progress: {today_confirmed}
• Total Today: {today_completed + today_confirmed}

**⏰ Order Age Analysis:**
• Recent (<2h): {len(recent_orders)}
• Normal (2-24h): {len(confirmed_orders) - len(stale_orders) - len(recent_orders)}
• Stale (>24h): {len(stale_orders)}

**🚚 Assignment Status:**
• Active Assignments: {active_assignments}
• Failed Assignments: {failed_assignments}
• Total Assignments: {len(assignments)}

**⚠️ Stale Orders (>24h):**
{stale_list}

**💡 Recommendations:**
{f"• Run cleanup to remove {len(stale_orders)} stale orders" if len(stale_orders) > 0 else "• System running smoothly"}
{f"• Check {failed_assignments} failed assignments" if failed_assignments > 0 else ""}"""

        keyboard = types.InlineKeyboardMarkup()
        if len(stale_orders) > 0 or failed_assignments > 0:
            keyboard.add(
                types.InlineKeyboardButton("🧹 Run Cleanup", callback_data="reset_daily_init")
            )
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="order_status"),
            types.InlineKeyboardButton("🔙 Back", callback_data="system_management")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error showing order status: {e}")
        handle_analytics_error(call, str(e), "order_status")

def is_rate_limited(user_id):
    now = time.time()
    dq = user_action_times[user_id]
    dq.append(now)
    if len(dq) == RATE_LIMIT and now - dq[0] < RATE_PERIOD:
        return True
    return False

def is_authorized_user(user_id: int) -> bool:
    """Check if user is authorized to access management functions"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

def create_main_menu_keyboard():
    """Create the main management menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    # Delivery Personnel Management
    keyboard.add(
        types.InlineKeyboardButton("👥 Personnel Management", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics Dashboard", callback_data="mgmt_analytics")
    )
    
    # System Management
    keyboard.add(
        types.InlineKeyboardButton("📈 Reports", callback_data="mgmt_reports"),
        types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings")
    )

    # Issue Management
    keyboard.add(
        types.InlineKeyboardButton("🚨 Issue Reports", callback_data="mgmt_issues"),
        types.InlineKeyboardButton("🔧 System Management", callback_data="system_management")
    )

    # System Info
    keyboard.add(
        types.InlineKeyboardButton("ℹ️ System Info", callback_data="mgmt_info")
    )

    # Utilities
    keyboard.add(
        types.InlineKeyboardButton("🔄 Refresh Data", callback_data="mgmt_refresh")
    )
    return keyboard

def create_personnel_menu_keyboard():
    """Create personnel management menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        types.InlineKeyboardButton("➕ Add Personnel", callback_data="pers_add"),
        types.InlineKeyboardButton("➖ Remove Personnel", callback_data="pers_remove")
    )
    keyboard.add(
        types.InlineKeyboardButton("📋 List All", callback_data="pers_list"),
        types.InlineKeyboardButton("🔍 Search", callback_data="pers_search")
    )
    keyboard.add(
        types.InlineKeyboardButton("📊 Performance", callback_data="pers_performance"),
        types.InlineKeyboardButton("🔧 Manage Status", callback_data="pers_status")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )
    
    return keyboard

def create_analytics_menu_keyboard():
    """Create analytics dashboard menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)

    keyboard.add(
        types.InlineKeyboardButton("📅 Daily Summary", callback_data="analytics_daily"),
        types.InlineKeyboardButton("📊 Weekly Summary", callback_data="analytics_weekly")
    )
    keyboard.add(
        types.InlineKeyboardButton("📈 Monthly Summary", callback_data="analytics_monthly"),
        types.InlineKeyboardButton("🌟 All-Time Stats", callback_data="analytics_alltime")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔢 Transaction Counts", callback_data="analytics_transactions"),
        types.InlineKeyboardButton("🚚 Delivery Stats", callback_data="analytics_delivery")
    )
    keyboard.add(
        types.InlineKeyboardButton("💰 Payroll System", callback_data="analytics_payroll"),
        types.InlineKeyboardButton("💹 Trend Analysis", callback_data="analytics_trends")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )

    return keyboard

def log_admin_action(action, details, user_id):
    # Use integer milliseconds timestamp for Firestore-safe document ID
    ts = int(time.time() * 1000)
    log_entry = {
        'timestamp': datetime.utcnow().isoformat(),
        'user_id': user_id,
        'action': action,
        'details': details
    }
    set_data(f"admin_audit_logs/{ts}_{user_id}", log_entry)

def notify_admin_error(error_text):
    try:
        from src.config import SYSTEM_ADMIN_ID
        management_bot.send_message(SYSTEM_ADMIN_ID, f"❗️[ERROR] {error_text}")
    except Exception as e:
        logger.error(f"Failed to notify admin: {e}")

# Enhanced Firebase Data Integrity Functions
def validate_firebase_operation(operation_name: str, path: str, data=None) -> bool:
    """Validate Firebase operation parameters before execution"""
    try:
        if not path or not isinstance(path, str):
            logger.error(f"Invalid path for {operation_name}: {path}")
            return False

        if operation_name in ['set', 'update'] and data is None:
            logger.error(f"No data provided for {operation_name} operation at {path}")
            return False

        # Check for potentially dangerous paths
        dangerous_patterns = ['..', '//', 'null', 'undefined', '<', '>', '|', '\\']
        if any(pattern in path for pattern in dangerous_patterns):
            logger.error(f"Potentially dangerous path detected: {path}")
            return False

        # Validate data size for set/update operations
        if operation_name in ['set', 'update'] and data is not None:
            try:
                import json
                data_size = len(json.dumps(data, default=str))
                if data_size > 1000000:  # 1MB limit
                    logger.error(f"Data too large for Firebase operation: {data_size} bytes")
                    return False
            except Exception as size_error:
                logger.warning(f"Could not validate data size: {size_error}")

        return True
    except Exception as e:
        logger.error(f"Error validating Firebase operation: {e}")
        return False

def safe_firebase_set(path: str, data: any, max_retries: int = 3) -> bool:
    """Safely set data in Firebase with validation and retry logic"""
    if not validate_firebase_operation('set', path, data):
        return False

    for attempt in range(max_retries):
        try:
            success = set_data(path, data)
            if success:
                logger.info(f"Successfully set data at {path} (attempt {attempt + 1})")
                return True
            else:
                logger.warning(f"Failed to set data at {path} (attempt {attempt + 1})")
        except Exception as e:
            logger.error(f"Exception setting data at {path} (attempt {attempt + 1}): {e}")

        if attempt < max_retries - 1:
            import time
            time.sleep(1 * (attempt + 1))  # Exponential backoff

    logger.error(f"Failed to set data at {path} after {max_retries} attempts")
    notify_admin_error(f"Firebase set operation failed at {path} after {max_retries} attempts")
    return False

def safe_firebase_update(path: str, data: any, max_retries: int = 3) -> bool:
    """Safely update data in Firebase with validation and retry logic"""
    if not validate_firebase_operation('update', path, data):
        return False

    for attempt in range(max_retries):
        try:
            success = update_data(path, data)
            if success:
                logger.info(f"Successfully updated data at {path} (attempt {attempt + 1})")
                return True
            else:
                logger.warning(f"Failed to update data at {path} (attempt {attempt + 1})")
        except Exception as e:
            logger.error(f"Exception updating data at {path} (attempt {attempt + 1}): {e}")

        if attempt < max_retries - 1:
            import time
            time.sleep(1 * (attempt + 1))  # Exponential backoff

    logger.error(f"Failed to update data at {path} after {max_retries} attempts")
    notify_admin_error(f"Firebase update operation failed at {path} after {max_retries} attempts")
    return False

def safe_firebase_delete(path: str, max_retries: int = 3) -> bool:
    """Safely delete data from Firebase with validation and retry logic"""
    if not validate_firebase_operation('delete', path):
        return False

    for attempt in range(max_retries):
        try:
            success = delete_data(path)
            if success:
                logger.info(f"Successfully deleted data at {path} (attempt {attempt + 1})")
                return True
            else:
                logger.warning(f"Failed to delete data at {path} (attempt {attempt + 1})")
        except Exception as e:
            logger.error(f"Exception deleting data at {path} (attempt {attempt + 1}): {e}")

        if attempt < max_retries - 1:
            import time
            time.sleep(1 * (attempt + 1))  # Exponential backoff

    logger.error(f"Failed to delete data at {path} after {max_retries} attempts")
    notify_admin_error(f"Firebase delete operation failed at {path} after {max_retries} attempts")
    return False

def verify_personnel_data_integrity(personnel_id: str, personnel_data: dict) -> bool:
    """Verify that personnel data was correctly stored in Firebase"""
    try:
        # Retrieve the data back from Firebase
        stored_data = get_data(f"delivery_personnel/{personnel_id}")

        if not stored_data:
            logger.error(f"Personnel data not found in Firebase after storage: {personnel_id}")
            return False

        # Verify critical fields
        critical_fields = ['name', 'phone_number', 'telegram_id', 'status']
        for field in critical_fields:
            if stored_data.get(field) != personnel_data.get(field):
                logger.error(f"Data integrity check failed for {personnel_id}.{field}: "
                           f"expected {personnel_data.get(field)}, got {stored_data.get(field)}")
                return False

        logger.info(f"Data integrity verified for personnel {personnel_id}")
        return True

    except Exception as e:
        logger.error(f"Error verifying personnel data integrity: {e}")
        return False

def handle_start(message):
    """Handle start command and show main menu"""
    user_id = message.from_user.id
    
    if not is_admin(user_id):
        management_bot.reply_to(
            message,
            "❌ **Access Denied**\n\n"
            "You are not authorized to access the Management Bot.\n"
            "Contact system administrator for access.",
            parse_mode='Markdown'
        )
        return
    
    if is_rate_limited(user_id):
        management_bot.reply_to(message, "⏳ Too many actions. Please wait a minute and try again.")
        return
    
    try:
        log_admin_action('start_command', {}, user_id)
        
        welcome_text = f"""
🏢 **Wiz Aroma Management Bot**

Welcome to the comprehensive management interface!

**Available Functions:**
👥 **Personnel Management** - Add/remove delivery personnel
📊 **Analytics Dashboard** - View system performance metrics
📈 **Reports** - Generate detailed reports
💰 **Earnings** - Track delivery personnel earnings
🔄 **Data Management** - Refresh and maintain system data

Select an option below to get started:
    """
        
        management_bot.send_message(
            message.chat.id,
            welcome_text,
            reply_markup=create_main_menu_keyboard(),
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Exception in handle_start: {e}")
        notify_admin_error(f"Exception in handle_start: {e}")
        management_bot.reply_to(message, "❗️ An error occurred. The admin has been notified.")

def handle_callback_query(call):
    """Handle all callback queries"""
    user_id = call.from_user.id
    
    if not is_admin(user_id):
        management_bot.answer_callback_query(
            call.id,
            "❌ You are not authorized to use this bot."
        )
        return
    
    if is_rate_limited(user_id):
        management_bot.answer_callback_query(call.id, "⏳ Too many actions. Please wait a minute and try again.")
        return
    
    try:
        # Validate callback data
        if not call.data or not isinstance(call.data, str):
            management_bot.answer_callback_query(
                call.id,
                "❌ Invalid callback data",
                show_alert=True
            )
            return

        log_admin_action('callback', {'data': call.data}, user_id)

        # Show processing message for long actions
        if call.data.startswith("mgmt_analytics"):
            management_bot.answer_callback_query(call.id, "⏳ Processing analytics...", show_alert=False)
        
        # Main menu navigation
        if call.data == "mgmt_main":
            show_main_menu(call)
        elif call.data == "mgmt_personnel":
            show_personnel_menu(call)
        elif call.data == "mgmt_analytics":
            show_analytics_menu(call)
        elif call.data == "mgmt_reports":
            show_reports_menu(call)
        elif call.data == "mgmt_earnings":
            show_earnings_menu(call)
        elif call.data == "mgmt_issues":
            show_issue_reports(call)
        elif call.data == "mgmt_refresh":
            refresh_system_data(call)
        elif call.data == "mgmt_info":
            show_system_info(call)
        
        # Personnel management - ID-based selection
        elif call.data == "pers_add":
            start_add_personnel(call)
        elif call.data == "pers_edit_select":
            start_edit_personnel_selection(call)
        elif call.data == "pers_delete_select":
            start_delete_personnel_selection(call)
        elif call.data == "pers_refresh":
            # Invalidate cache and refresh personnel menu
            invalidate_personnel_cache()
            show_personnel_menu(call)
        elif call.data.startswith("pers_"):
            handle_personnel_action(call)

        # Individual personnel management
        elif call.data.startswith("pers_manage_"):
            show_individual_personnel_menu(call)
        elif call.data.startswith("pers_view_"):
            show_personnel_details(call)
        elif call.data.startswith("pers_edit_"):
            start_edit_personnel(call)
        elif call.data.startswith("pers_delete_"):
            confirm_delete_personnel(call)
        elif call.data.startswith("confirm_delete_"):
            execute_delete_personnel(call)
        elif call.data.startswith("pers_update_"):
            handle_personnel_update(call)

        # Edit specific fields
        elif call.data.startswith("edit_name_"):
            start_edit_name(call)
        elif call.data.startswith("edit_phone_"):
            start_edit_phone(call)
        elif call.data.startswith("edit_telegram_"):
            start_edit_telegram_id(call)
        elif call.data.startswith("save_edit_"):
            save_personnel_edit(call)

        # Analytics
        elif call.data.startswith("analytics_"):
            handle_analytics_action(call)
        
        # Reports
        elif call.data.startswith("reports_"):
            handle_reports_action(call)
        
        # Earnings
        elif call.data.startswith("earnings_"):
            handle_earnings_action(call)
        elif call.data == "payroll_details":
            show_individual_earnings(call)

        # Detail views for analytics
        elif call.data == "daily_details":
            show_daily_details(call)
        elif call.data == "weekly_details":
            show_weekly_details(call)
        elif call.data == "show_more_details":
            show_more_analytics_details(call)

        # System Management and Reset Operations
        elif call.data == "system_management":
            show_system_management_menu(call)
        elif call.data == "reset_season_init":
            initiate_seasonal_reset(call)
        elif call.data == "reset_season_confirm":
            confirm_seasonal_reset(call)
        elif call.data == "reset_daily_init":
            initiate_daily_cleanup(call)
        elif call.data == "cleanup_standard":
            execute_standard_cleanup(call)
        elif call.data == "cleanup_quick":
            execute_quick_cleanup(call)
        elif call.data == "system_status":
            show_system_status(call)
        elif call.data == "audit_log":
            show_audit_log(call)
        elif call.data == "order_status":
            show_order_status(call)

        # Order Reset Management
        elif call.data == "order_reset_menu":
            show_order_reset_menu(call)
        elif call.data == "order_reset_global":
            initiate_global_order_reset(call)
        elif call.data == "order_reset_individual":
            show_individual_reset_selection(call)
        elif call.data == "order_reset_status":
            show_order_reset_status(call)
        elif call.data.startswith("reset_individual_"):
            handle_individual_reset(call)
        elif call.data.startswith("confirm_global_reset"):
            execute_global_order_reset(call)
        elif call.data.startswith("confirm_individual_reset_"):
            execute_individual_order_reset(call)

        # Personnel removal confirmation
        elif call.data.startswith("remove_personnel_"):
            handle_personnel_removal(call)
        elif call.data.startswith("confirm_remove_"):
            confirm_personnel_removal(call)

        else:
            management_bot.answer_callback_query(
                call.id,
                "❓ Unknown action. Please try again.",
                show_alert=True
            )
    
    except Exception as e:
        logger.error(f"Exception in handle_callback_query: {e}")
        notify_admin_error(f"Exception in handle_callback_query: {e}")
        management_bot.answer_callback_query(
            call.id,
            "❗️ An error occurred. The admin has been notified.",
            show_alert=True
        )

def show_main_menu(call):
    """Show the main management menu"""
    # Answer the callback query first
    management_bot.answer_callback_query(call.id, "🏢 Loading main menu...")

    welcome_text = """
🏢 **Wiz Aroma Management Bot**

**Management Dashboard**
Select a function to manage your delivery system:
    """

    # Use safe message editing with fallback mechanisms
    if not safe_edit_message(call, welcome_text, create_main_menu_keyboard()):
        management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

def show_personnel_menu(call):
    """Show simplified personnel management with ID-based selection"""
    try:
        # Answer the callback query first
        management_bot.answer_callback_query(call.id, "👥 Loading personnel management...")

        # Force refresh personnel data from Firebase for real-time updates
        personnel_data = refresh_personnel_data()
        availability_data = refresh_availability_data()

        if not personnel_data:
            text = """👥 *Personnel Management*

❌ *No Personnel Found*

There are no delivery personnel registered in the system."""

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("➕ Add New Personnel", callback_data="pers_add")
            )
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Main Menu", callback_data="mgmt_main")
            )
        else:
            # Get earnings data for all personnel
            all_earnings = get_all_personnel_earnings()

            # Create header message with earnings
            text = f"""👥 *Personnel Management*

*Total Personnel:* {len(personnel_data)}

*Personnel List:*"""

            # Create numbered list of personnel with real-time status and earnings info
            personnel_list = list(personnel_data.items())
            for index, (personnel_id, person) in enumerate(personnel_list, 1):
                # Get basic info
                name = escape_markdown(person.get('name', 'Unknown'))
                phone = escape_markdown(person.get('phone_number', 'N/A'))
                status = person.get('status', 'offline')

                # Get real-time capacity
                from src.utils.delivery_personnel_utils import get_real_time_capacity
                current_capacity = get_real_time_capacity(personnel_id)
                max_capacity = person.get('max_capacity', 5)

                # Get earnings for this personnel
                earnings = all_earnings.get(personnel_id, {})
                daily_earnings = earnings.get('daily_earnings', 0.0)
                weekly_earnings = earnings.get('weekly_earnings', 0.0)

                # Status emoji with capacity info
                status_emoji = "🟢" if status == "available" else "🔴" if status == "busy" else "⚫"
                capacity_info = f"({current_capacity}/{max_capacity})"

                # Add to numbered list with real-time status, capacity, and earnings
                daily_str = escape_markdown(f"{daily_earnings:.2f}")
                weekly_str = escape_markdown(f"{weekly_earnings:.2f}")
                text += f"\n{index}\\. {status_emoji} {name} {capacity_info} \\- {phone} \\- Daily: {daily_str} birr \\- Weekly: {weekly_str} birr"

            # Create enhanced keyboard with refresh functionality
            keyboard = types.InlineKeyboardMarkup(row_width=2)

            # Row 1: Add and Edit
            keyboard.add(
                types.InlineKeyboardButton("➕ Add New Personnel", callback_data="pers_add"),
                types.InlineKeyboardButton("✏️ Edit Personnel", callback_data="pers_edit_select")
            )

            # Row 2: Delete and Refresh
            keyboard.add(
                types.InlineKeyboardButton("🗑️ Delete Personnel", callback_data="pers_delete_select"),
                types.InlineKeyboardButton("🔄 Refresh Data", callback_data="pers_refresh")
            )

            # Row 3: Back
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Main Menu", callback_data="mgmt_main")
            )



        # Use safe message editing with comprehensive fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in show_personnel_menu: {e}")
        try:
            management_bot.answer_callback_query(call.id, "❌ Error loading personnel data", show_alert=True)
        except:
            pass

def start_edit_personnel_selection(call):
    """Start the process of selecting personnel to edit by ID"""
    management_bot.answer_callback_query(call.id, "✏️ Select personnel to edit...")

    # Get current personnel data to show the list
    personnel_data = get_data("delivery_personnel") or {}
    all_earnings = get_all_personnel_earnings()

    if not personnel_data:
        management_bot.edit_message_text(
            "❌ *No Personnel Found*\n\nThere are no delivery personnel to edit\\.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='MarkdownV2'
        )
        return

    # Create personnel list for selection
    text = """✏️ *Edit Personnel*

*Personnel List:*"""

    personnel_list = list(personnel_data.items())
    for index, (personnel_id, person) in enumerate(personnel_list, 1):
        name = escape_markdown(person.get('name', 'Unknown'))
        phone = escape_markdown(person.get('phone_number', 'N/A'))

        # Get earnings for this personnel
        earnings = all_earnings.get(personnel_id, {})
        daily_earnings = earnings.get('daily_earnings', 0.0)
        weekly_earnings = earnings.get('weekly_earnings', 0.0)

        # Escape decimal numbers to prevent MarkdownV2 parsing errors
        daily_str = escape_markdown(f"{daily_earnings:.2f}")
        weekly_str = escape_markdown(f"{weekly_earnings:.2f}")

        text += f"\n{index}\\. {name} \\- {phone} \\- Daily: {daily_str} birr \\- Weekly: {weekly_str} birr"

    text += """\n\nPlease enter the personnel ID number \\(1, 2, 3, etc\\.\\) from the list above to edit\\.

*Example:* Send `2` to edit the second person in the list\\."""

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='MarkdownV2'
    )

    # Set up message handler for ID input
    management_bot.register_next_step_handler(call.message, process_edit_personnel_id)

def start_delete_personnel_selection(call):
    """Start the process of selecting personnel to delete by ID"""
    management_bot.answer_callback_query(call.id, "🗑️ Select personnel to delete...")

    # Get current personnel data to show the list
    personnel_data = get_data("delivery_personnel") or {}
    all_earnings = get_all_personnel_earnings()

    if not personnel_data:
        management_bot.edit_message_text(
            "❌ *No Personnel Found*\n\nThere are no delivery personnel to delete\\.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='MarkdownV2'
        )
        return

    # Create personnel list for selection
    text = """🗑️ *Delete Personnel*

*Personnel List:*"""

    personnel_list = list(personnel_data.items())
    for index, (personnel_id, person) in enumerate(personnel_list, 1):
        name = escape_markdown(person.get('name', 'Unknown'))
        phone = escape_markdown(person.get('phone_number', 'N/A'))

        # Get earnings for this personnel
        earnings = all_earnings.get(personnel_id, {})
        daily_earnings = earnings.get('daily_earnings', 0.0)
        weekly_earnings = earnings.get('weekly_earnings', 0.0)

        # Escape decimal numbers to prevent MarkdownV2 parsing errors
        daily_str = escape_markdown(f"{daily_earnings:.2f}")
        weekly_str = escape_markdown(f"{weekly_earnings:.2f}")

        text += f"\n{index}\\. {name} \\- {phone} \\- Daily: {daily_str} birr \\- Weekly: {weekly_str} birr"

    text += """\n\nPlease enter the personnel ID number \\(1, 2, 3, etc\\.\\) from the list above to delete\\.

*Example:* Send `3` to delete the third person in the list\\.

⚠️ *Warning:* This action cannot be undone\\!"""

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='MarkdownV2'
    )

    # Set up message handler for ID input
    management_bot.register_next_step_handler(call.message, process_delete_personnel_id)

def process_edit_personnel_id(message):
    """Process the personnel ID for editing"""
    try:
        # Validate input
        personnel_id_input = message.text.strip()

        if not personnel_id_input.isdigit():
            management_bot.reply_to(
                message,
                "❌ *Invalid Input*\n\nPlease enter a valid number \\(1, 2, 3, etc\\.\\)",
                parse_mode='MarkdownV2'
            )
            return

        personnel_index = int(personnel_id_input) - 1  # Convert to 0-based index

        # Get personnel data
        personnel_data = get_data("delivery_personnel") or {}
        personnel_list = list(personnel_data.items())

        if personnel_index < 0 or personnel_index >= len(personnel_list):
            management_bot.reply_to(
                message,
                f"❌ *Invalid Personnel ID*\n\nPlease enter a number between 1 and {len(personnel_list)}\\.",
                parse_mode='MarkdownV2'
            )
            return

        # Get the selected personnel
        personnel_id, person = personnel_list[personnel_index]
        show_edit_personnel_details(message, personnel_id, person)

    except Exception as e:
        logger.error(f"Error processing edit personnel ID: {e}")
        management_bot.reply_to(
            message,
            "❌ *Error*\n\nAn error occurred\\. Please try again\\.",
            parse_mode='MarkdownV2'
        )

def process_delete_personnel_id(message):
    """Process the personnel ID for deletion"""
    try:
        # Validate input
        personnel_id_input = message.text.strip()

        if not personnel_id_input.isdigit():
            management_bot.reply_to(
                message,
                "❌ *Invalid Input*\n\nPlease enter a valid number \\(1, 2, 3, etc\\.\\)",
                parse_mode='MarkdownV2'
            )
            return

        personnel_index = int(personnel_id_input) - 1  # Convert to 0-based index

        # Get personnel data
        personnel_data = get_data("delivery_personnel") or {}
        personnel_list = list(personnel_data.items())

        if personnel_index < 0 or personnel_index >= len(personnel_list):
            management_bot.reply_to(
                message,
                f"❌ *Invalid Personnel ID*\n\nPlease enter a number between 1 and {len(personnel_list)}\\.",
                parse_mode='MarkdownV2'
            )
            return

        # Get the selected personnel
        personnel_id, person = personnel_list[personnel_index]
        show_delete_confirmation(message, personnel_id, person)

    except Exception as e:
        logger.error(f"Error processing delete personnel ID: {e}")
        management_bot.reply_to(
            message,
            "❌ *Error*\n\nAn error occurred\\. Please try again\\.",
            parse_mode='MarkdownV2'
        )

def show_edit_personnel_details(message, personnel_id, person):
    """Show edit options for selected personnel"""
    try:
        # Safely get and escape personnel data
        name = escape_markdown(person.get('name', 'Unknown'))
        phone = escape_markdown(person.get('phone_number', 'N/A'))
        telegram_id = escape_markdown(person.get('telegram_id', 'N/A'))

        text = f"""✏️ *Edit Personnel: {name}*

*Current Information:*
• *Name:* {name}
• *Phone:* {phone}
• *Telegram ID:* {telegram_id}

*Select what you want to edit:*"""

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("📝 Edit Name", callback_data=f"edit_name_{personnel_id}"),
            types.InlineKeyboardButton("📞 Edit Phone", callback_data=f"edit_phone_{personnel_id}")
        )
        keyboard.add(
            types.InlineKeyboardButton("📱 Edit Telegram ID", callback_data=f"edit_telegram_{personnel_id}")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
        )

        management_bot.reply_to(
            message,
            text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error showing edit personnel details: {e}")
        management_bot.reply_to(
            message,
            "❌ *Error*\n\nFailed to load personnel details\\.",
            parse_mode='MarkdownV2'
        )

def show_delete_confirmation(message, personnel_id, person):
    """Show delete confirmation for selected personnel"""
    try:
        # Safely get personnel data
        name = escape_markdown(person.get('name', 'Unknown'))
        phone = escape_markdown(person.get('phone_number', 'N/A'))
        status = person.get('status', 'offline')

        text = f"""🗑️ *Confirm Personnel Deletion*

*Personnel to Delete:*
• *Name:* {name}
• *Phone:* {phone}
• *Status:* {status.title()}

⚠️ *WARNING:* This action will permanently remove this personnel from the system\\. All their data will be deleted\\.

*This action cannot be undone\\!*

Are you sure you want to proceed?"""

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("✅ Yes, Delete", callback_data=f"confirm_delete_{personnel_id}"),
            types.InlineKeyboardButton("❌ Cancel", callback_data="mgmt_personnel")
        )

        management_bot.reply_to(
            message,
            text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error showing delete confirmation: {e}")
        management_bot.reply_to(
            message,
            "❌ *Error*\n\nFailed to load personnel details\\.",
            parse_mode='MarkdownV2'
        )

def show_analytics_menu(call):
    """Show analytics dashboard menu with reset state awareness and time-based reset integration"""
    # Answer the callback query first
    management_bot.answer_callback_query(call.id, "📊 Loading analytics dashboard...")

    # Check and execute time-based resets before showing analytics
    from src.utils.time_based_reset_utils import check_and_execute_time_based_resets, get_reset_status
    from src.utils.analytics_counter_system import get_analytics_counter_summary

    # Execute any needed time-based resets
    reset_results = check_and_execute_time_based_resets()
    executed_resets = [k for k, v in reset_results.items() if v]

    # Get reset status for display
    reset_status = get_reset_status()

    # Get analytics counter summary
    try:
        counter_summary = get_analytics_counter_summary()
    except Exception as e:
        logger.error(f"Error getting analytics counter summary: {e}")
        counter_summary = {}

    # Check if system has been recently reset
    completed_orders = get_data("completed_orders") or {}
    audit_data = get_data("system_audit_log") or {}

    # Check for recent seasonal reset
    recent_reset = None
    for log_id, entry in audit_data.items():
        if entry.get('operation_type') == 'seasonal_reset' and entry.get('status') == 'completed':
            try:
                reset_time = datetime.fromisoformat(entry.get('completed_at', ''))
                if (datetime.now() - reset_time).days < 7:  # Within last 7 days
                    recent_reset = reset_time.strftime('%Y-%m-%d')
                    break
            except:
                continue

    if len(completed_orders) == 0 and recent_reset:
        text = f"""📊 **Analytics Dashboard**

🆕 **New Season Started: {recent_reset}**

The system has been reset and is ready for fresh analytics data.

**Current Status:**
• All counters reset to zero
• Personnel earnings cleared
• Revenue tracking restarted
• Performance metrics reset

**Available Analytics:**
• Daily/Weekly/Monthly summaries
• Transaction counts and trends
• Delivery performance metrics
• System usage statistics

**Note:** Analytics will populate as new orders are processed."""
    elif len(completed_orders) == 0:
        text = """📊 **Analytics Dashboard**

**System Status:** No completed orders found

**Available Analytics:**
• Daily/Weekly/Monthly summaries
• Transaction counts and trends
• Delivery performance metrics
• System usage statistics

**Note:** Analytics will show zero values until orders are completed."""
    else:
        # Add time-based reset status information
        reset_info = ""
        if executed_resets:
            reset_info = f"\n🔄 **Recent Resets:** {', '.join(executed_resets)}"

        # Get tracking data for display
        tracking_data = reset_status.get('tracking_data', {})
        last_daily = tracking_data.get('last_daily_reset', 'Never')
        last_weekly = tracking_data.get('last_weekly_reset', 'Never')
        last_monthly = tracking_data.get('last_monthly_reset', 'Never')

        text = f"""📊 **Analytics Dashboard**

**System Status:** {len(completed_orders)} completed orders tracked{reset_info}

**Time-Based Reset Status:**
• Daily: {last_daily}
• Weekly: {last_weekly}
• Monthly: {last_monthly}

**Available Analytics:**
• Daily/Weekly/Monthly summaries
• Transaction counts and trends
• Delivery performance metrics
• System usage statistics

Select an analytics option:"""

    # Use safe message editing with fallback mechanisms
    if not safe_edit_message(call, text, create_analytics_menu_keyboard()):
        management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

def show_reports_menu(call):
    """Show comprehensive reports menu"""
    management_bot.answer_callback_query(call.id, "📈 Loading reports menu...")

    text = """
📈 **Reports Dashboard**

Select a report type to view detailed analytics and performance metrics:

**Time-based Reports:**
• Daily, Weekly, Monthly, and All-time summaries
• Revenue and profit breakdowns
• Order completion statistics

**Personnel Reports:**
• Individual performance metrics
• Delivery assignments and completions
• Earnings and payroll summaries
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("📅 Daily Report", callback_data="reports_daily"),
        types.InlineKeyboardButton("📊 Weekly Report", callback_data="reports_weekly")
    )
    keyboard.add(
        types.InlineKeyboardButton("📈 Monthly Report", callback_data="reports_monthly"),
        types.InlineKeyboardButton("🌟 All-Time Report", callback_data="reports_alltime")
    )
    keyboard.add(
        types.InlineKeyboardButton("👥 Personnel Report", callback_data="reports_personnel")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_issue_reports(call):
    """Show customer issue reports and delivery problems"""
    try:
        management_bot.answer_callback_query(call.id, "🚨 Loading issue reports...")

        # Get order data
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        personnel_data = get_data("delivery_personnel") or {}

        # Find all orders with issues
        issue_orders = []

        # Check confirmed orders for issues
        for order_id, order in confirmed_orders.items():
            delivery_status = order.get('delivery_status', '')
            issue_reported_at = order.get('issue_reported_at')
            confirmation_count = order.get('confirmation_attempts', 0)

            if delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2:
                issue_orders.append({
                    'order_number': order.get('order_number', order_id),
                    'customer_phone': order.get('phone_number', 'N/A'),
                    'restaurant_name': order.get('restaurant_name', 'Unknown'),
                    'delivery_address': order.get('delivery_location', 'N/A'),
                    'issue_reported_at': issue_reported_at or 'Multiple attempts',
                    'delivery_status': delivery_status,
                    'assigned_to': order.get('assigned_to', 'Unassigned'),
                    'subtotal': order.get('subtotal', 0),
                    'delivery_fee': order.get('delivery_fee', 0),
                    'status': 'Active Issue'
                })

        # Check completed orders for issues
        for order_id, order in completed_orders.items():
            delivery_status = order.get('delivery_status', '')
            issue_reported_at = order.get('issue_reported_at')
            confirmation_count = order.get('confirmation_attempts', 0)

            if delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2:
                issue_orders.append({
                    'order_number': order.get('order_number', order_id),
                    'customer_phone': order.get('phone_number', 'N/A'),
                    'restaurant_name': order.get('restaurant_name', 'Unknown'),
                    'delivery_address': order.get('delivery_location', 'N/A'),
                    'issue_reported_at': issue_reported_at or 'Multiple attempts',
                    'delivery_status': delivery_status,
                    'assigned_to': order.get('assigned_to', 'Unassigned'),
                    'subtotal': order.get('subtotal', 0),
                    'delivery_fee': order.get('delivery_fee', 0),
                    'status': 'Resolved Issue'
                })

        # Sort by issue report time (most recent first)
        issue_orders.sort(key=lambda x: x.get('issue_reported_at', ''), reverse=True)

        if not issue_orders:
            text = """🎉 **No Issues Found!**

**System Status:** All orders are running smoothly
**Issue Reports:** 0 active issues
**Customer Satisfaction:** Excellent

This is great news! No customers have reported delivery issues."""
        else:
            # Create summary
            active_issues = len([o for o in issue_orders if o['status'] == 'Active Issue'])
            resolved_issues = len([o for o in issue_orders if o['status'] == 'Resolved Issue'])

            text = f"""🚨 **Issue Reports Dashboard**

**📊 Summary:**
• Active Issues: {active_issues}
• Resolved Issues: {resolved_issues}
• Total Issues: {len(issue_orders)}

**🔍 Recent Issues:**"""

            # Show up to 10 most recent issues
            for i, issue in enumerate(issue_orders[:10], 1):
                status_emoji = "🔴" if issue['status'] == 'Active Issue' else "🟢"
                personnel_name = "Unassigned"

                # Get personnel name if assigned
                if issue['assigned_to'] and issue['assigned_to'] != 'Unassigned':
                    for p_id, p_data in personnel_data.items():
                        if p_id == issue['assigned_to']:
                            personnel_name = p_data.get('name', 'Unknown')
                            break

                total_amount = float(issue['subtotal']) + float(issue['delivery_fee'])

                text += f"""

{status_emoji} **Issue #{i}**
📦 Order: #{issue['order_number']}
🏪 Restaurant: {issue['restaurant_name']}
📞 Customer: {issue['customer_phone']}
📍 Address: {issue['delivery_address'][:50]}{'...' if len(issue['delivery_address']) > 50 else ''}
👤 Assigned to: {personnel_name}
💰 Amount: {total_amount:.2f} birr
🕐 Reported: {issue['issue_reported_at']}
📊 Status: {issue['status']}"""

            if len(issue_orders) > 10:
                text += f"\n\n... and {len(issue_orders) - 10} more issues"

        keyboard = types.InlineKeyboardMarkup()
        if issue_orders:
            keyboard.add(
                types.InlineKeyboardButton("🔄 Refresh", callback_data="mgmt_issues"),
                types.InlineKeyboardButton("📊 Details", callback_data="issues_details")
            )
        else:
            keyboard.add(
                types.InlineKeyboardButton("🔄 Refresh", callback_data="mgmt_issues")
            )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
        )

        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in show_issue_reports: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_earnings_menu(call):
    """Show comprehensive earnings menu"""
    management_bot.answer_callback_query(call.id, "💰 Loading earnings menu...")

    text = """
💰 **Earnings Dashboard**

Manage and view delivery personnel earnings, payroll calculations, and payment tracking:

**Earnings Overview:**
• Individual personnel earnings breakdown
• Payroll calculations (50% delivery fee sharing)
• Weekly and monthly earnings summaries

**Payment Tracking:**
• Completed delivery counts
• Earnings based on customer-confirmed orders
• Real-time payroll calculations
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("📊 Earnings Summary", callback_data="earnings_summary"),
        types.InlineKeyboardButton("👤 Individual Earnings", callback_data="earnings_individual")
    )
    keyboard.add(
        types.InlineKeyboardButton("💵 Payroll Breakdown", callback_data="earnings_payroll"),
        types.InlineKeyboardButton("📅 Weekly Earnings", callback_data="earnings_weekly")
    )
    keyboard.add(
        types.InlineKeyboardButton("📈 Monthly Earnings", callback_data="earnings_monthly")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

# Report Functions
def show_daily_report(call):
    """Show comprehensive daily report"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Generating daily report...")

        today = datetime.now().strftime("%Y-%m-%d")

        # Get order data
        completed_orders_data = get_data("completed_orders") or {}
        confirmed_orders_data = get_data("confirmed_orders") or {}

        # Filter today's orders
        today_completed = [o for o in completed_orders_data.values() if o.get('completed_at', '').startswith(today)]
        today_confirmed = [o for o in confirmed_orders_data.values() if o.get('confirmed_at', '').startswith(today)]

        # Calculate metrics
        total_orders = len(today_completed) + len(today_confirmed)
        food_revenue = sum(float(o.get('subtotal', 0)) for o in today_completed)
        delivery_revenue = sum(float(o.get('delivery_fee', 0)) for o in today_completed)
        total_revenue = food_revenue + delivery_revenue

        # Get personnel data
        personnel_data = get_data("delivery_personnel") or {}
        active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])

        # Format numbers without escaping decimal points
        food_revenue_str = format_number(food_revenue, 2)
        delivery_revenue_str = format_number(delivery_revenue, 2)
        total_revenue_str = format_number(total_revenue, 2)
        success_rate = (len(today_completed)/total_orders*100) if total_orders > 0 else 0
        company_profit = delivery_revenue * 0.5
        personnel_utilization = (active_personnel/len(personnel_data)*100) if len(personnel_data) > 0 else 0

        text = f"""
📅 **Daily Report - {today}**

**📊 Executive Summary:**
• Total Orders: {total_orders}
• Completed Orders: {len(today_completed)}
• Orders in Progress: {len(today_confirmed)}
• Success Rate: {format_number(success_rate, 1)}%

**💰 Financial Performance:**
• Food Revenue: {food_revenue_str} birr
• Delivery Revenue: {delivery_revenue_str} birr
• Total Revenue: {total_revenue_str} birr
• Company Profit: {format_number(company_profit, 2)} birr

**👥 Operations:**
• Active Personnel: {active_personnel}
• Total Personnel: {len(personnel_data)}
• Personnel Utilization: {format_number(personnel_utilization, 1)}%

**📈 Key Metrics:**
• Average Order Value: {escape_markdown(f"{(total_revenue/len(today_completed)) if len(today_completed) > 0 else 0:.2f}")} birr
• Revenue per Personnel: {escape_markdown(f"{(total_revenue/len(personnel_data)) if len(personnel_data) > 0 else 0:.2f}")} birr
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="reports_daily"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_reports")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in daily report: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_weekly_report(call):
    """Show comprehensive weekly report"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Generating weekly report...")

        # Calculate week range
        today = datetime.now()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)
        week_range = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"

        # Get order data
        completed_orders_data = get_data("completed_orders") or {}
        confirmed_orders_data = get_data("confirmed_orders") or {}

        # Filter week's orders
        week_completed = []
        week_confirmed = []

        for order in completed_orders_data.values():
            completed_at = order.get('completed_at', '')
            if completed_at and len(completed_at) >= 10:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if week_start.date() <= order_date.date() <= week_end.date():
                        week_completed.append(order)
                except (ValueError, TypeError):
                    continue

        for order in confirmed_orders_data.values():
            confirmed_at = order.get('confirmed_at', '')
            if confirmed_at and len(confirmed_at) >= 10:
                try:
                    order_date = datetime.strptime(confirmed_at[:10], '%Y-%m-%d')
                    if week_start.date() <= order_date.date() <= week_end.date():
                        week_confirmed.append(order)
                except (ValueError, TypeError):
                    continue

        # Calculate metrics
        total_orders = len(week_completed) + len(week_confirmed)
        food_revenue = sum(float(o.get('subtotal', 0)) for o in week_completed)
        delivery_revenue = sum(float(o.get('delivery_fee', 0)) for o in week_completed)
        total_revenue = food_revenue + delivery_revenue

        # Calculate growth (simplified)
        daily_avg = total_orders / 7

        # Format numbers without escaping decimal points
        food_revenue_str = format_number(food_revenue, 2)
        delivery_revenue_str = format_number(delivery_revenue, 2)
        total_revenue_str = format_number(total_revenue, 2)
        daily_avg_str = format_number(daily_avg, 1)

        text = f"""
📊 **Weekly Report**
**Period:** {week_range}

**📊 Performance Summary:**
• Total Orders: {total_orders}
• Completed Orders: {len(week_completed)}
• Orders in Progress: {len(week_confirmed)}
• Daily Average: {daily_avg_str} orders

**💰 Financial Performance:**
• Food Revenue: {food_revenue_str} birr
• Delivery Revenue: {delivery_revenue_str} birr
• Total Revenue: {total_revenue_str} birr
• Company Profit: {escape_markdown(f"{delivery_revenue * 0.5:.2f}")} birr
• Personnel Earnings: {escape_markdown(f"{delivery_revenue * 0.5:.2f}")} birr

**📈 Key Insights:**
• Completion Rate: {escape_markdown(f"{(len(week_completed)/total_orders*100) if total_orders > 0 else 0:.1f}")}%
• Average Order Value: {escape_markdown(f"{(total_revenue/len(week_completed)) if len(week_completed) > 0 else 0:.2f}")} birr
• Revenue Growth: Week-over-week analysis
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="reports_weekly"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_reports")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in weekly report: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def refresh_system_data(call):
    """Refresh system data from Firebase"""
    management_bot.answer_callback_query(call.id, "🔄 Refreshing data...")
    # Implementation will be added in next phase

def show_system_info(call):
    """Show system information"""
    management_bot.answer_callback_query(call.id, "ℹ️ System info feature coming soon!")

# Additional Report Functions
def show_monthly_report(call):
    """Show comprehensive monthly report"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Generating monthly report...")

        # Calculate month range
        today = datetime.now()
        month_start = today.replace(day=1)
        if today.month == 12:
            month_end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        month_range = f"{month_start.strftime('%Y-%m-%d')} to {month_end.strftime('%Y-%m-%d')}"

        # Get order data
        completed_orders_data = get_data("completed_orders") or {}
        confirmed_orders_data = get_data("confirmed_orders") or {}

        # Filter month's orders
        month_completed = []
        month_confirmed = []

        for order in completed_orders_data.values():
            completed_at = order.get('completed_at', '')
            if completed_at and len(completed_at) >= 10:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if month_start.date() <= order_date.date() <= month_end.date():
                        month_completed.append(order)
                except (ValueError, TypeError):
                    continue

        for order in confirmed_orders_data.values():
            confirmed_at = order.get('confirmed_at', '')
            if confirmed_at and len(confirmed_at) >= 10:
                try:
                    order_date = datetime.strptime(confirmed_at[:10], '%Y-%m-%d')
                    if month_start.date() <= order_date.date() <= month_end.date():
                        month_confirmed.append(order)
                except (ValueError, TypeError):
                    continue

        # Calculate metrics
        total_orders = len(month_completed) + len(month_confirmed)
        food_revenue = sum(float(o.get('subtotal', 0)) for o in month_completed)
        delivery_revenue = sum(float(o.get('delivery_fee', 0)) for o in month_completed)
        total_revenue = food_revenue + delivery_revenue
        days_in_month = (month_end - month_start).days + 1

        # Format numbers without escaping decimal points
        food_revenue_str = format_number(food_revenue, 2)
        delivery_revenue_str = format_number(delivery_revenue, 2)
        total_revenue_str = format_number(total_revenue, 2)
        daily_avg_str = format_number((total_orders/days_in_month), 1)

        text = f"""
📈 **Monthly Report**
**Period:** {month_range}

**📊 Performance Summary:**
• Total Orders: {total_orders}
• Completed Orders: {len(month_completed)}
• Orders in Progress: {len(month_confirmed)}
• Daily Average: {daily_avg_str} orders

**💰 Financial Performance:**
• Food Revenue: {food_revenue_str} birr
• Delivery Revenue: {delivery_revenue_str} birr
• Total Revenue: {total_revenue_str} birr
• Company Profit: {escape_markdown(f"{delivery_revenue * 0.5:.2f}")} birr
• Personnel Earnings: {escape_markdown(f"{delivery_revenue * 0.5:.2f}")} birr

**📈 Monthly Insights:**
• Completion Rate: {escape_markdown(f"{(len(month_completed)/total_orders*100) if total_orders > 0 else 0:.1f}")}%
• Average Order Value: {escape_markdown(f"{(total_revenue/len(month_completed)) if len(month_completed) > 0 else 0:.2f}")} birr
• Days in Month: {days_in_month}
• Revenue per Day: {escape_markdown(f"{(total_revenue/days_in_month):.2f}")} birr
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="reports_monthly"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_reports")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in monthly report: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_alltime_report(call):
    """Show comprehensive all-time report"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Generating all-time report...")

        # Get all order data
        completed_orders_data = get_data("completed_orders") or {}
        confirmed_orders_data = get_data("confirmed_orders") or {}

        # Calculate all-time metrics
        total_completed = len(completed_orders_data)
        total_confirmed = len(confirmed_orders_data)
        total_orders = total_completed + total_confirmed

        # Calculate revenue
        food_revenue = sum(float(o.get('subtotal', 0)) for o in completed_orders_data.values())
        delivery_revenue = sum(float(o.get('delivery_fee', 0)) for o in completed_orders_data.values())
        total_revenue = food_revenue + delivery_revenue

        # Calculate system uptime
        first_order_date = None
        for order in completed_orders_data.values():
            completed_at = order.get('completed_at', '')
            if completed_at and len(completed_at) >= 10:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if first_order_date is None or order_date < first_order_date:
                        first_order_date = order_date
                except (ValueError, TypeError):
                    continue

        system_days = (datetime.now() - first_order_date).days + 1 if first_order_date else 0

        # Escape numbers
        food_revenue_str = escape_markdown(f"{food_revenue:.2f}")
        delivery_revenue_str = escape_markdown(f"{delivery_revenue:.2f}")
        total_revenue_str = escape_markdown(f"{total_revenue:.2f}")

        text = f"""
🌟 **All-Time Report**
**System Active Since:** {first_order_date.strftime('%Y-%m-%d') if first_order_date else 'N/A'}

**📊 Lifetime Performance:**
• Total Orders: {total_orders}
• Completed Orders: {total_completed}
• Orders in Progress: {total_confirmed}
• System Days: {system_days}

**💰 Lifetime Financial Performance:**
• Food Revenue: {food_revenue_str} birr
• Delivery Revenue: {delivery_revenue_str} birr
• Total Revenue: {total_revenue_str} birr
• Company Profit: {escape_markdown(f"{delivery_revenue * 0.5:.2f}")} birr
• Personnel Earnings: {escape_markdown(f"{delivery_revenue * 0.5:.2f}")} birr

**📈 Lifetime Insights:**
• Success Rate: {escape_markdown(f"{(total_completed/total_orders*100) if total_orders > 0 else 0:.1f}")}%
• Average Order Value: {escape_markdown(f"{(total_revenue/total_completed) if total_completed > 0 else 0:.2f}")} birr
• Orders per Day: {escape_markdown(f"{(total_completed/system_days) if system_days > 0 else 0:.1f}")}
• Revenue per Day: {escape_markdown(f"{(total_revenue/system_days) if system_days > 0 else 0:.2f}")} birr
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="reports_alltime"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_reports")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in all-time report: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_personnel_report(call):
    """Show personnel performance report"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Generating personnel report...")

        # Get personnel and assignment data
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        completed_orders_data = get_data("completed_orders") or {}
        confirmed_orders_data = get_data("confirmed_orders") or {}

        if not personnel_data:
            text = "❌ **No Personnel Data**\n\nNo delivery personnel found in the system."
        else:
            # Calculate personnel statistics
            personnel_stats = []

            for personnel_id, person in personnel_data.items():
                name = person.get('name', 'Unknown')
                status = person.get('status', 'offline')

                # Count assignments, completions, and issues
                assignments = 0
                completed = 0
                total_earnings = 0
                issue_count = 0

                for assignment_id, assignment in assignments_data.items():
                    if assignment.get('personnel_id') == personnel_id:
                        assignments += 1
                        if assignment.get('status') == 'delivered':
                            completed += 1
                            # Calculate earnings from completed orders
                            order_number = assignment.get('order_number', '')
                            for order_id, order in completed_orders_data.items():
                                if order.get('order_number') == order_number:
                                    delivery_fee = float(order.get('delivery_fee', 0))
                                    total_earnings += delivery_fee * 0.5
                                    break

                # Count issues for this personnel
                all_orders = {**completed_orders_data, **confirmed_orders_data}
                for order_id, order in all_orders.items():
                    delivery_status = order.get('delivery_status', '')
                    issue_reported_at = order.get('issue_reported_at')
                    confirmation_count = order.get('confirmation_attempts', 0)
                    assigned_to = order.get('assigned_to')

                    if assigned_to == personnel_id and (delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2):
                        issue_count += 1

                completion_rate = (completed / assignments * 100) if assignments > 0 else 0
                issue_rate = (issue_count / assignments * 100) if assignments > 0 else 0

                personnel_stats.append({
                    'name': name,
                    'status': status,
                    'assignments': assignments,
                    'completed': completed,
                    'completion_rate': completion_rate,
                    'issue_count': issue_count,
                    'issue_rate': issue_rate,
                    'earnings': total_earnings
                })

            # Sort by completion rate
            personnel_stats.sort(key=lambda x: x['completion_rate'], reverse=True)

            # Create personnel breakdown
            personnel_breakdown = []
            for i, stats in enumerate(personnel_stats[:10], 1):  # Show top 10
                name = stats['name']
                completed = stats['completed']
                rate = stats['completion_rate']
                issue_count = stats['issue_count']
                issue_rate = stats['issue_rate']
                earnings = stats['earnings']
                status = stats['status']

                status_emoji = "🟢" if status == "available" else "🔴" if status == "busy" else "⚫"
                issue_emoji = "🚨" if issue_count > 0 else "✅"

                personnel_breakdown.append(
                    f"{i}. {status_emoji} {name}: {completed} deliveries ({rate:.1f}%) {issue_emoji} {issue_count} issues ({issue_rate:.1f}%) → {earnings:.2f} birr"
                )

            personnel_list = '\n'.join(personnel_breakdown) if personnel_breakdown else "• No personnel data available"

            # Calculate summary
            total_assignments = sum(stats['assignments'] for stats in personnel_stats)
            total_completed = sum(stats['completed'] for stats in personnel_stats)
            total_issues = sum(stats['issue_count'] for stats in personnel_stats)
            total_earnings = sum(stats['earnings'] for stats in personnel_stats)
            avg_completion_rate = sum(stats['completion_rate'] for stats in personnel_stats) / len(personnel_stats) if personnel_stats else 0
            avg_issue_rate = sum(stats['issue_rate'] for stats in personnel_stats) / len(personnel_stats) if personnel_stats else 0

            text = f"""
👥 **Personnel Performance Report**

**📊 Summary:**
• Total Personnel: {len(personnel_data)}
• Total Assignments: {total_assignments}
• Total Completions: {total_completed}
• Total Issues: {total_issues}
• Average Completion Rate: {escape_markdown(f"{avg_completion_rate:.1f}")}%
• Average Issue Rate: {escape_markdown(f"{avg_issue_rate:.1f}")}%
• Total Personnel Earnings: {escape_markdown(f"{total_earnings:.2f}")} birr

**🏆 Personnel Rankings (Top 10):**
{personnel_list}

**📈 Performance Insights:**
• Active Personnel: {len([p for p in personnel_data.values() if p.get('status') == 'available'])}
• Busy Personnel: {len([p for p in personnel_data.values() if p.get('status') == 'busy'])}
• Offline Personnel: {len([p for p in personnel_data.values() if p.get('status') == 'offline'])}
            """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="reports_personnel"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_reports")
        )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error in personnel report: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

# Earnings Functions
def show_earnings_summary(call):
    """Show comprehensive earnings summary with real-time data"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading earnings summary...")

        # Force refresh analytics data for real-time earnings calculations
        analytics_data = refresh_analytics_data()

        # Get data from refreshed collections
        personnel_data = analytics_data.get('personnel', {})
        assignments_data = analytics_data.get('assignments', {})
        completed_orders_data = analytics_data.get('completed_orders', {})

        # Calculate total earnings using new cost accounting system
        completed_orders_list = list(completed_orders_data.values())
        revenue_breakdown = calculate_revenue_breakdown(completed_orders_list)

        total_delivery_fees = revenue_breakdown['delivery_fees_cash'] + revenue_breakdown['delivery_fees_points']
        total_personnel_earnings = revenue_breakdown['personnel_earnings']
        total_company_profit = revenue_breakdown['company_profit']
        delivery_fees_cash = revenue_breakdown['delivery_fees_cash']
        delivery_fees_points = revenue_breakdown['delivery_fees_points']
        cash_revenue = revenue_breakdown['cash_revenue']
        point_payment_costs = revenue_breakdown['point_payment_costs']

        # Calculate personnel breakdown
        personnel_earnings = {}
        for assignment_id, assignment in assignments_data.items():
            if assignment.get('status') == 'delivered':
                personnel_id = assignment.get('personnel_id', '')
                order_number = assignment.get('order_number', '')

                # Find corresponding completed order
                for order_id, order in completed_orders_data.items():
                    if order.get('order_number') == order_number:
                        delivery_fee = float(order.get('delivery_fee', 0))
                        earnings = delivery_fee * 0.5

                        if personnel_id not in personnel_earnings:
                            personnel_earnings[personnel_id] = 0
                        personnel_earnings[personnel_id] += earnings
                        break

        # Get top earners
        top_earners = []
        for personnel_id, earnings in sorted(personnel_earnings.items(), key=lambda x: x[1], reverse=True)[:5]:
            person = personnel_data.get(personnel_id, {})
            name = person.get('name', 'Unknown')
            top_earners.append(f"• {name}: {escape_markdown(f'{earnings:.2f}')} birr")

        top_earners_str = '\n'.join(top_earners) if top_earners else "• No earnings data available"

        # Calculate averages
        avg_earnings_per_personnel = total_personnel_earnings / len(personnel_data) if len(personnel_data) > 0 else 0
        total_completed_orders = len(completed_orders_data)
        avg_earnings_per_order = total_personnel_earnings / total_completed_orders if total_completed_orders > 0 else 0

        text = f"""
💰 **Earnings Summary**

**📊 Total Earnings Overview:**
• Delivery Fees (Cash): {escape_markdown(f"{delivery_fees_cash:.2f}")} birr
• Delivery Fees (Points): {escape_markdown(f"{delivery_fees_points:.0f}")} points
• Personnel Earnings: {escape_markdown(f"{total_personnel_earnings:.2f}")} birr (50% of all delivery fees)

**{"📈" if total_company_profit >= 0 else "📉"} Company Profit/Loss Analysis:**
• Cash Revenue: {escape_markdown(f"{cash_revenue:.2f}")} birr (50% of cash delivery fees)
• Point Payment Costs: {escape_markdown(f"{point_payment_costs:.2f}")} birr (50% of point delivery fees)
• **Net Company Profit: {escape_markdown(f"{total_company_profit:.2f}")} birr**

**👥 Personnel Earnings:**
• Total Personnel: {len(personnel_data)}
• Average per Personnel: {escape_markdown(f"{avg_earnings_per_personnel:.2f}")} birr
• Average per Completed Order: {escape_markdown(f"{avg_earnings_per_order:.2f}")} birr

**🏆 Top Earners:**
{top_earners_str}

**📈 Earnings Metrics:**
• Total Completed Orders: {total_completed_orders}
• Active Earners: {len(personnel_earnings)}
• Earnings Distribution: 50/50 split model
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="earnings_summary"),
            types.InlineKeyboardButton("👤 Individual", callback_data="earnings_individual")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings")
        )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error in earnings summary: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_individual_earnings(call):
    """Show individual personnel earnings with ID-based selection"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading individual earnings...")

        # Get personnel data
        personnel_data = get_data("delivery_personnel") or {}

        if not personnel_data:
            text = "❌ **No Personnel Data**\n\nNo delivery personnel found in the system."
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings"))
        else:
            # Create numbered list of personnel
            personnel_list = []
            for i, (personnel_id, person) in enumerate(personnel_data.items(), 1):
                name = person.get('name', 'Unknown')
                status = person.get('status', 'offline')
                status_emoji = "🟢" if status == "available" else "🔴" if status == "busy" else "⚫"
                personnel_list.append(f"{i}. {status_emoji} {name}")

            personnel_str = '\n'.join(personnel_list)

            text = f"""
👤 **Individual Earnings Selection**

Select a delivery personnel to view detailed earnings:

**Personnel List:**
{personnel_str}

**Instructions:**
Reply with the number (1-{len(personnel_data)}) of the personnel you want to view earnings for.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings")
            )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

        # Register next step handler for personnel selection
        if personnel_data:
            management_bot.register_next_step_handler(call.message, process_individual_earnings_selection, personnel_data)

    except Exception as e:
        logger.error(f"Error in individual earnings: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def process_individual_earnings_selection(message, personnel_data):
    """Process individual earnings selection"""
    try:
        selection = int(message.text.strip())
        personnel_list = list(personnel_data.items())

        if 1 <= selection <= len(personnel_list):
            personnel_id, person = personnel_list[selection - 1]
            show_personnel_earnings_details(message, personnel_id, person)
        else:
            management_bot.send_message(
                message.chat.id,
                f"❌ Invalid selection. Please choose a number between 1 and {len(personnel_list)}."
            )
    except ValueError:
        management_bot.send_message(
            message.chat.id,
            "❌ Invalid input. Please enter a number."
        )
    except Exception as e:
        logger.error(f"Error processing earnings selection: {e}")
        management_bot.send_message(
            message.chat.id,
            f"❌ Error processing selection: {str(e)}"
        )

def show_personnel_earnings_details(message, personnel_id, person):
    """Show detailed earnings for specific personnel"""
    try:
        name = person.get('name', 'Unknown')

        # Get assignments and completed orders
        assignments_data = get_data("delivery_personnel_assignments") or {}
        completed_orders_data = get_data("completed_orders") or {}

        # Calculate earnings for this personnel
        total_earnings = 0
        completed_deliveries = 0
        delivery_details = []

        for assignment_id, assignment in assignments_data.items():
            if assignment.get('personnel_id') == personnel_id and assignment.get('status') == 'delivered':
                order_number = assignment.get('order_number', '')

                # Find corresponding completed order
                for order_id, order in completed_orders_data.items():
                    if order.get('order_number') == order_number:
                        delivery_fee = float(order.get('delivery_fee', 0))
                        earnings = delivery_fee * 0.5
                        total_earnings += earnings
                        completed_deliveries += 1

                        completed_at = order.get('completed_at', 'Unknown')
                        delivery_details.append(f"• {completed_at[:10]}: {escape_markdown(f'{earnings:.2f}')} birr")
                        break

        # Show recent deliveries (last 10)
        recent_deliveries = '\n'.join(delivery_details[-10:]) if delivery_details else "• No completed deliveries found"

        text = f"""
👤 **{name} - Earnings Details**

**📊 Summary:**
• Total Earnings: {escape_markdown(f"{total_earnings:.2f}")} birr
• Completed Deliveries: {completed_deliveries}
• Average per Delivery: {escape_markdown(f"{(total_earnings/completed_deliveries) if completed_deliveries > 0 else 0:.2f}")} birr

**📅 Recent Deliveries (Last 10):**
{recent_deliveries}

**💡 Earnings Calculation:**
• Earnings = 50% of delivery fee per completed order
• Only customer-confirmed orders count toward earnings
• Real-time calculation from Firebase data
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="earnings_individual"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings")
        )

        management_bot.send_message(
            message.chat.id,
            text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error showing personnel earnings details: {e}")
        management_bot.send_message(
            message.chat.id,
            f"❌ Error loading earnings details: {str(e)}"
        )

def handle_personnel_action(call):
    """Handle personnel management actions"""
    action = call.data.replace("pers_", "")

    if action == "add":
        start_add_personnel(call)
    elif action == "remove":
        start_remove_personnel(call)
    elif action == "list":
        show_personnel_list(call)
    elif action == "search":
        start_personnel_search(call)
    elif action == "performance":
        show_personnel_performance(call)
    elif action == "status":
        show_personnel_status_management(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown personnel action")

# Additional Earnings Functions
def show_payroll_breakdown(call):
    """Show detailed payroll breakdown"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading payroll breakdown...")

        # Get data
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        completed_orders_data = get_data("completed_orders") or {}

        if not personnel_data:
            text = "❌ **No Personnel Data**\n\nNo delivery personnel found in the system."
        else:
            # Calculate detailed payroll
            payroll_details = []
            total_company_payout = 0

            for personnel_id, person in personnel_data.items():
                name = person.get('name', 'Unknown')

                # Calculate earnings for this personnel
                personnel_earnings = 0
                completed_deliveries = 0

                for assignment_id, assignment in assignments_data.items():
                    if assignment.get('personnel_id') == personnel_id and assignment.get('status') == 'delivered':
                        order_number = assignment.get('order_number', '')

                        # Find corresponding completed order
                        for order_id, order in completed_orders_data.items():
                            if order.get('order_number') == order_number:
                                delivery_fee = float(order.get('delivery_fee', 0))
                                earnings = delivery_fee * 0.5
                                personnel_earnings += earnings
                                completed_deliveries += 1
                                break

                total_company_payout += personnel_earnings

                # Add to payroll details
                if personnel_earnings > 0:
                    payroll_details.append({
                        'name': name,
                        'deliveries': completed_deliveries,
                        'earnings': personnel_earnings
                    })

            # Sort by earnings
            payroll_details.sort(key=lambda x: x['earnings'], reverse=True)

            # Create payroll list
            payroll_list = []
            for i, details in enumerate(payroll_details, 1):
                name = details['name']
                deliveries = details['deliveries']
                earnings = details['earnings']
                payroll_list.append(f"{i}. {name}: {deliveries} deliveries → {escape_markdown(f'{earnings:.2f}')} birr")

            payroll_str = '\n'.join(payroll_list) if payroll_list else "• No earnings to pay out"

            text = f"""
💵 **Payroll Breakdown**

**📊 Payment Summary:**
• Total Personnel: {len(personnel_data)}
• Personnel with Earnings: {len(payroll_details)}
• Total Company Payout: {escape_markdown(f"{total_company_payout:.2f}")} birr

**💰 Individual Payroll:**
{payroll_str}

**📋 Payment Instructions:**
• Payments based on 50% of delivery fees
• Only customer-confirmed orders count
• Payment due for completed deliveries
• Real-time calculation from Firebase
            """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="earnings_payroll"),
            types.InlineKeyboardButton("📊 Summary", callback_data="earnings_summary")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in payroll breakdown: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_weekly_earnings(call):
    """Show weekly earnings breakdown"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading weekly earnings...")

        # Calculate week range
        today = datetime.now()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)
        week_range = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"

        # Get data
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        completed_orders_data = get_data("completed_orders") or {}

        # Filter week's completed orders
        week_completed_orders = []
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if week_start.date() <= order_date.date() <= week_end.date():
                        week_completed_orders.append(order)
                except:
                    continue

        # Calculate weekly earnings
        weekly_personnel_earnings = {}
        total_weekly_delivery_fees = 0

        for order in week_completed_orders:
            order_number = order.get('order_number', '')
            delivery_fee = float(order.get('delivery_fee', 0))
            total_weekly_delivery_fees += delivery_fee

            # Find assignment for this order
            for assignment_id, assignment in assignments_data.items():
                if assignment.get('order_number') == order_number and assignment.get('status') == 'delivered':
                    personnel_id = assignment.get('personnel_id', '')
                    if personnel_id:
                        earnings = delivery_fee * 0.5
                        if personnel_id not in weekly_personnel_earnings:
                            weekly_personnel_earnings[personnel_id] = 0
                        weekly_personnel_earnings[personnel_id] += earnings
                    break

        total_weekly_personnel_earnings = sum(weekly_personnel_earnings.values())
        total_weekly_company_profit = total_weekly_delivery_fees * 0.5

        # Create weekly earnings list
        weekly_earnings_list = []
        for personnel_id, earnings in sorted(weekly_personnel_earnings.items(), key=lambda x: x[1], reverse=True):
            person = personnel_data.get(personnel_id, {})
            name = person.get('name', 'Unknown')
            weekly_earnings_list.append(f"• {name}: {escape_markdown(f'{earnings:.2f}')} birr")

        weekly_earnings_str = '\n'.join(weekly_earnings_list) if weekly_earnings_list else "• No earnings this week"

        text = f"""
📅 **Weekly Earnings**
**Period:** {week_range}

**📊 Weekly Summary:**
• Total Delivery Fees: {escape_markdown(f"{total_weekly_delivery_fees:.2f}")} birr
• Personnel Earnings: {escape_markdown(f"{total_weekly_personnel_earnings:.2f}")} birr
• Company Profit: {escape_markdown(f"{total_weekly_company_profit:.2f}")} birr
• Completed Orders: {len(week_completed_orders)}

**👥 Personnel Earnings This Week:**
{weekly_earnings_str}

**📈 Weekly Performance:**
• Average per Order: {escape_markdown(f"{(total_weekly_personnel_earnings/len(week_completed_orders)) if len(week_completed_orders) > 0 else 0:.2f}")} birr
• Active Earners: {len(weekly_personnel_earnings)}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="earnings_weekly"),
            types.InlineKeyboardButton("📈 Monthly", callback_data="earnings_monthly")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in weekly earnings: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_monthly_earnings(call):
    """Show monthly earnings breakdown"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading monthly earnings...")

        # Calculate month range
        today = datetime.now()
        month_start = today.replace(day=1)
        if today.month == 12:
            month_end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        month_range = f"{month_start.strftime('%Y-%m-%d')} to {month_end.strftime('%Y-%m-%d')}"

        # Get data
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        completed_orders_data = get_data("completed_orders") or {}

        # Filter month's completed orders
        month_completed_orders = []
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if month_start.date() <= order_date.date() <= month_end.date():
                        month_completed_orders.append(order)
                except:
                    continue

        # Calculate monthly earnings
        monthly_personnel_earnings = {}
        total_monthly_delivery_fees = 0

        for order in month_completed_orders:
            order_number = order.get('order_number', '')
            delivery_fee = float(order.get('delivery_fee', 0))
            total_monthly_delivery_fees += delivery_fee

            # Find assignment for this order
            for assignment_id, assignment in assignments_data.items():
                if assignment.get('order_number') == order_number and assignment.get('status') == 'delivered':
                    personnel_id = assignment.get('personnel_id', '')
                    if personnel_id:
                        earnings = delivery_fee * 0.5
                        if personnel_id not in monthly_personnel_earnings:
                            monthly_personnel_earnings[personnel_id] = 0
                        monthly_personnel_earnings[personnel_id] += earnings
                    break

        total_monthly_personnel_earnings = sum(monthly_personnel_earnings.values())
        total_monthly_company_profit = total_monthly_delivery_fees * 0.5

        # Create monthly earnings list
        monthly_earnings_list = []
        for personnel_id, earnings in sorted(monthly_personnel_earnings.items(), key=lambda x: x[1], reverse=True):
            person = personnel_data.get(personnel_id, {})
            name = person.get('name', 'Unknown')
            monthly_earnings_list.append(f"• {name}: {escape_markdown(f'{earnings:.2f}')} birr")

        monthly_earnings_str = '\n'.join(monthly_earnings_list) if monthly_earnings_list else "• No earnings this month"

        # Calculate days in month for averages
        days_in_month = (month_end - month_start).days + 1

        text = f"""
📈 **Monthly Earnings**
**Period:** {month_range}

**📊 Monthly Summary:**
• Total Delivery Fees: {escape_markdown(f"{total_monthly_delivery_fees:.2f}")} birr
• Personnel Earnings: {escape_markdown(f"{total_monthly_personnel_earnings:.2f}")} birr
• Company Profit: {escape_markdown(f"{total_monthly_company_profit:.2f}")} birr
• Completed Orders: {len(month_completed_orders)}

**👥 Personnel Earnings This Month:**
{monthly_earnings_str}

**📈 Monthly Performance:**
• Days in Month: {days_in_month}
• Avg Earnings/Day: {escape_markdown(f"{(total_monthly_personnel_earnings/days_in_month):.2f}")} birr
• Avg per Order: {escape_markdown(f"{(total_monthly_personnel_earnings/len(month_completed_orders)) if len(month_completed_orders) > 0 else 0:.2f}")} birr
• Active Earners: {len(monthly_personnel_earnings)}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="earnings_monthly"),
            types.InlineKeyboardButton("📅 Weekly", callback_data="earnings_weekly")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_earnings")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in monthly earnings: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_individual_personnel_menu(call):
    """Show management menu for individual personnel"""
    personnel_id = call.data.replace("pers_manage_", "")
    management_bot.answer_callback_query(call.id, "👤 Loading personnel options...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    status = person.get('status', 'offline')
    is_verified = person.get('is_verified', False)

    # Status and verification emojis
    status_emoji = {
        'available': '✅',
        'busy': '🔄',
        'offline': '⭕',
        'inactive': '❌'
    }.get(status, '❓')

    verified_emoji = '✅' if is_verified else '⚠️'

    text = f"""
👤 **Personnel Management**

**{status_emoji}{verified_emoji} {name}**
ID: `{personnel_id}`

**Quick Actions:**
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("👁️ View Details", callback_data=f"pers_view_{personnel_id}"),
        types.InlineKeyboardButton("✏️ Edit Info", callback_data=f"pers_edit_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔧 Change Status", callback_data=f"pers_status_{personnel_id}"),
        types.InlineKeyboardButton("📊 Performance", callback_data=f"pers_perf_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🗑️ Delete Personnel", callback_data=f"pers_delete_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_details(call):
    """Show detailed view of personnel information"""
    personnel_id = call.data.replace("pers_view_", "")
    management_bot.answer_callback_query(call.id, "👁️ Loading personnel details...")

    # Get all personnel-related data
    personnel_data = get_data("delivery_personnel") or {}
    availability_data = get_data("delivery_personnel_availability") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}
    zones_data = get_data("delivery_personnel_zones") or {}
    performance_data = get_data("delivery_personnel_performance") or {}

    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    # Extract personnel information
    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    telegram_id = person.get('telegram_id', 'N/A')
    email = person.get('email', 'N/A')
    status = person.get('status', 'offline')
    is_verified = person.get('is_verified', False)
    vehicle_type = person.get('vehicle_type', 'N/A')
    max_capacity = person.get('max_capacity', 5)
    current_capacity = capacity_data.get(personnel_id, 0)
    service_areas = person.get('service_areas', [])
    created_at = person.get('created_at', 'N/A')
    last_active = person.get('last_active', 'N/A')

    # Performance metrics
    perf = performance_data.get(personnel_id, {})
    total_deliveries = perf.get('total_deliveries', person.get('total_deliveries', 0))
    successful_deliveries = perf.get('successful_deliveries', person.get('successful_deliveries', 0))
    rating = perf.get('average_rating', person.get('rating', 5.0))

    # Calculate issue count for this personnel
    issue_count = 0
    completed_orders = get_data("completed_orders") or {}
    confirmed_orders = get_data("confirmed_orders") or {}
    assignments_data = get_data("delivery_personnel_assignments") or {}

    # Count issues from orders assigned to this personnel
    all_orders = {**completed_orders, **confirmed_orders}
    for order_id, order in all_orders.items():
        delivery_status = order.get('delivery_status', '')
        issue_reported_at = order.get('issue_reported_at')
        confirmation_count = order.get('confirmation_attempts', 0)
        assigned_to = order.get('assigned_to')

        # Check if this order was assigned to this personnel and has issues
        if assigned_to == personnel_id and (delivery_status == 'delivery_issue' or issue_reported_at or confirmation_count >= 2):
            issue_count += 1

    # Calculate success rate and issue rate
    success_rate = (successful_deliveries / total_deliveries * 100) if total_deliveries > 0 else 0
    issue_rate = (issue_count / total_deliveries * 100) if total_deliveries > 0 else 0

    # Status emojis
    status_emoji = {
        'available': '✅',
        'busy': '🔄',
        'offline': '⭕',
        'inactive': '❌'
    }.get(status, '❓')

    verified_emoji = '✅' if is_verified else '⚠️'

    # Safely format the personnel details message
    safe_name = escape_markdown(name)
    safe_phone = escape_markdown(phone)
    safe_telegram_id = escape_markdown(telegram_id)
    safe_email = escape_markdown(email)
    safe_personnel_id = escape_markdown(personnel_id)
    safe_vehicle_type = escape_markdown(vehicle_type.title())
    safe_status = escape_markdown(status.title())
    safe_service_areas = escape_markdown(', '.join(service_areas) if service_areas else 'None assigned')
    safe_created_at = escape_markdown(created_at)
    safe_last_active = escape_markdown(last_active)

    text = f"""👁️ *Personnel Details*

{status_emoji}{verified_emoji} *{safe_name}*

*📋 Basic Information:*
• *ID:* `{safe_personnel_id}`
• *Phone:* {safe_phone}
• *Telegram ID:* {safe_telegram_id}
• *Email:* {safe_email}
• *Vehicle:* {safe_vehicle_type}
• *Status:* {safe_status}
• *Verified:* {'Yes' if is_verified else 'No'}

*📊 Capacity & Performance:*
• *Current Orders:* {current_capacity}/{max_capacity}
• *Total Deliveries:* {total_deliveries}
• *Successful:* {successful_deliveries}
• *Success Rate:* {success_rate:.1f}%
• *Issue Reports:* {issue_count} ({issue_rate:.1f}%)
• *Rating:* {rating:.1f}/5.0

*🗺️ Service Areas:*
{safe_service_areas}

*📅 Timeline:*
• *Created:* {safe_created_at}
• *Last Active:* {safe_last_active}"""

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✏️ Edit", callback_data=f"pers_edit_{personnel_id}"),
        types.InlineKeyboardButton("🔧 Status", callback_data=f"pers_status_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("📊 Performance", callback_data=f"pers_perf_{personnel_id}"),
        types.InlineKeyboardButton("🗑️ Delete", callback_data=f"pers_delete_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Management", callback_data=f"pers_manage_{personnel_id}")
    )

    # Use safe message editing with comprehensive fallback mechanisms
    if not safe_edit_message(call, text, keyboard):
        management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

def handle_analytics_action(call):
    """Handle analytics actions"""
    action = call.data.replace("analytics_", "")

    if action == "daily":
        show_daily_analytics(call)
    elif action == "weekly":
        show_weekly_analytics(call)
    elif action == "monthly":
        show_monthly_analytics(call)
    elif action == "alltime":
        show_alltime_analytics(call)
    elif action == "transactions":
        show_transaction_analytics(call)
    elif action == "delivery":
        show_delivery_analytics(call)
    elif action == "payroll":
        show_payroll_analytics(call)
    elif action == "trends":
        show_trend_analytics(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown analytics action")

def start_edit_personnel(call):
    """Start editing personnel information"""
    personnel_id = call.data.replace("pers_edit_", "")
    management_bot.answer_callback_query(call.id, "✏️ Loading edit options...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    # Safely get and escape personnel data
    name = escape_markdown(person.get('name', 'Unknown'))
    safe_phone = escape_markdown(person.get('phone_number', 'N/A'))
    safe_telegram_id = escape_markdown(person.get('telegram_id', 'N/A'))

    # Get earnings data
    earnings = get_personnel_earnings_summary(personnel_id)

    # Escape earnings decimal numbers
    daily_earnings_str = escape_markdown(f"{earnings.get('daily_earnings', 0.0):.2f}")
    weekly_earnings_str = escape_markdown(f"{earnings.get('weekly_earnings', 0.0):.2f}")

    text = f"""✏️ *Edit Personnel: {name}*

*Current Information:*
• *Name:* {name}
• *Phone:* {safe_phone}
• *Telegram ID:* {safe_telegram_id}
• *Daily Earnings:* {daily_earnings_str} birr
• *Weekly Earnings:* {weekly_earnings_str} birr

*⚠️ Note:* Editing preserves all earnings data and performance metrics.

*Select what you want to edit:*"""

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("📝 Name", callback_data=f"edit_name_{personnel_id}"),
        types.InlineKeyboardButton("📞 Phone", callback_data=f"edit_phone_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("📱 Telegram ID", callback_data=f"edit_telegram_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Management", callback_data="mgmt_personnel")
    )

    # Send message with error handling
    try:
        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Error displaying edit personnel menu: {e}")
        # Fallback to plain text
        try:
            plain_text = text.replace('*', '').replace('_', '').replace('`', '')
            management_bot.edit_message_text(
                plain_text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=keyboard
            )
        except Exception as fallback_error:
            logger.error(f"Fallback error in edit personnel menu: {fallback_error}")
            management_bot.answer_callback_query(call.id, "❌ Error loading edit menu", show_alert=True)

def confirm_delete_personnel(call):
    """Show confirmation dialog for personnel deletion"""
    personnel_id = call.data.replace("pers_delete_", "")
    management_bot.answer_callback_query(call.id, "🗑️ Preparing deletion confirmation...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    status = person.get('status', 'offline')
    total_deliveries = person.get('total_deliveries', 0)

    # Get earnings data
    earnings = get_personnel_earnings_summary(personnel_id)

    # Escape earnings decimal numbers for deletion confirmation
    daily_earnings_str = escape_markdown(f"{earnings.get('daily_earnings', 0.0):.2f}")
    weekly_earnings_str = escape_markdown(f"{earnings.get('weekly_earnings', 0.0):.2f}")
    lifetime_earnings_str = escape_markdown(f"{earnings.get('total_lifetime_earnings', 0.0):.2f}")

    text = f"""
⚠️ **Confirm Personnel Deletion**

**Personnel to Delete:**
• **Firestore ID:** `{personnel_id}`
• **Name:** {name}
• **Phone:** {phone}
• **Status:** {status.title()}
• **Total Deliveries:** {total_deliveries}
• **Daily Earnings:** {daily_earnings_str} birr
• **Weekly Earnings:** {weekly_earnings_str} birr
• **Lifetime Earnings:** {lifetime_earnings_str} birr

**⚠️ WARNING:**
This action will permanently remove this personnel from the system.
**ALL DATA WILL BE DELETED:**
• Personnel record
• Earnings history (daily, weekly, lifetime)
• Performance metrics
• Availability status
• Zone assignments
• Capacity information

**This action cannot be undone!**

Are you sure you want to proceed?
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✅ Yes, Delete", callback_data=f"confirm_delete_{personnel_id}"),
        types.InlineKeyboardButton("❌ Cancel", callback_data=f"pers_view_{personnel_id}")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def handle_reports_action(call):
    """Handle reports actions"""
    action = call.data.replace("reports_", "")

    if action == "daily":
        show_daily_report(call)
    elif action == "weekly":
        show_weekly_report(call)
    elif action == "monthly":
        show_monthly_report(call)
    elif action == "alltime":
        show_alltime_report(call)
    elif action == "personnel":
        show_personnel_report(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown reports action")

def handle_earnings_action(call):
    """Handle earnings actions"""
    action = call.data.replace("earnings_", "")

    if action == "summary":
        show_earnings_summary(call)
    elif action == "individual":
        show_individual_earnings(call)
    elif action == "payroll":
        show_payroll_breakdown(call)
    elif action == "weekly":
        show_weekly_earnings(call)
    elif action == "monthly":
        show_monthly_earnings(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown earnings action")

def execute_delete_personnel(call):
    """Execute personnel deletion after confirmation"""
    personnel_id = call.data.replace("confirm_delete_", "")
    management_bot.answer_callback_query(call.id, "🗑️ Deleting personnel...")

    try:
        # Get personnel data before deletion for logging
        personnel_data = get_data("delivery_personnel") or {}
        person = personnel_data.get(personnel_id)

        if not person:
            management_bot.answer_callback_query(
                call.id,
                "❌ Personnel not found!",
                show_alert=True
            )
            return

        name = person.get('name', 'Unknown')

        # Delete from all Firebase collections
        from src.firebase_db import delete_data

        success = True
        error_messages = []

        # Delete from main personnel collection
        if not delete_data(f"delivery_personnel/{personnel_id}"):
            success = False
            error_messages.append("delivery_personnel")

        # Delete from availability
        if not delete_data(f"delivery_personnel_availability/{personnel_id}"):
            error_messages.append("availability")

        # Delete from capacity
        if not delete_data(f"delivery_personnel_capacity/{personnel_id}"):
            error_messages.append("capacity")

        # Delete from zones
        if not delete_data(f"delivery_personnel_zones/{personnel_id}"):
            error_messages.append("zones")

        # Delete from performance
        if not delete_data(f"delivery_personnel_performance/{personnel_id}"):
            error_messages.append("performance")

        # Delete from earnings
        if not delete_data(f"delivery_personnel_earnings/{personnel_id}"):
            error_messages.append("earnings")

        # Delete from assignments (remove all assignments for this personnel)
        assignments_data = get_data("delivery_personnel_assignments") or {}
        assignments_to_remove = []
        for assignment_id, assignment in assignments_data.items():
            if assignment.get('personnel_id') == personnel_id:
                assignments_to_remove.append(assignment_id)

        for assignment_id in assignments_to_remove:
            if not delete_data(f"delivery_personnel_assignments/{assignment_id}"):
                error_messages.append(f"assignment_{assignment_id}")

        # Delete from capacity tracking cache
        if not delete_data(f"delivery_personnel_capacity_tracking/{personnel_id}"):
            error_messages.append("capacity_tracking")

        # Delete from any broadcast message tracking
        if not delete_data(f"delivery_personnel_cache/{personnel_id}"):
            error_messages.append("cache")

        # Also remove from the main collections using alternative method
        try:
            # Get and update main collections
            personnel_data = get_data("delivery_personnel") or {}
            availability_data = get_data("delivery_personnel_availability") or {}
            capacity_data = get_data("delivery_personnel_capacity") or {}
            zones_data = get_data("delivery_personnel_zones") or {}
            performance_data = get_data("delivery_personnel_performance") or {}
            earnings_data = get_data("delivery_personnel_earnings") or {}

            # Remove from all collections
            personnel_data.pop(personnel_id, None)
            availability_data.pop(personnel_id, None)
            capacity_data.pop(personnel_id, None)
            zones_data.pop(personnel_id, None)
            performance_data.pop(personnel_id, None)
            earnings_data.pop(personnel_id, None)

            # Save updated collections with enhanced operations
            delete_operations = [
                ("delivery_personnel", personnel_data),
                ("delivery_personnel_availability", availability_data),
                ("delivery_personnel_capacity", capacity_data),
                ("delivery_personnel_zones", zones_data),
                ("delivery_personnel_performance", performance_data),
                ("delivery_personnel_earnings", earnings_data)
            ]

            failed_deletes = []
            for path, data in delete_operations:
                if not safe_firebase_set(path, data):
                    failed_deletes.append(path)
                    logger.error(f"Failed to update {path} during personnel deletion")

            if failed_deletes:
                error_messages.extend(failed_deletes)
                notify_admin_error(f"Failed to update collections during deletion of {personnel_id}: {failed_deletes}")

        except Exception as e:
            logger.error(f"Error during collection cleanup: {e}")
            error_messages.append("collection_cleanup")

        # Invalidate all caches to ensure real-time updates
        invalidate_personnel_cache(personnel_id)
        invalidate_personnel_cache()  # Invalidate global cache too

        # Refresh delivery personnel data models to ensure order broadcasting excludes deleted personnel
        try:
            from src.utils.delivery_personnel_utils import refresh_delivery_personnel_data
            refresh_delivery_personnel_data()
            logger.info(f"Refreshed delivery personnel data models after deletion of {personnel_id}")
        except Exception as e:
            logger.error(f"Failed to refresh delivery personnel data models: {e}")

        # Log the deletion for audit purposes
        logger.info(f"Personnel {personnel_id} ({name}) deleted by admin {call.from_user.id}")

        if success and not error_messages:
            text = f"""
✅ **Personnel Deleted Successfully**

**{name}** (ID: `{personnel_id}`) has been permanently removed from the system.

**Deleted Data:**
• Personnel record
• Earnings history (daily, weekly, lifetime)
• Performance metrics
• Availability status
• Capacity information
• Zone assignments
• Active assignments
• Cache data

The personnel can no longer access the delivery system and all their data has been permanently removed from Firebase.

**Real-time Effect:** The personnel will no longer receive order broadcasts and will not appear in any reports or analytics.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
            )

        else:
            text = f"""
⚠️ **Partial Deletion Warning**

**{name}** was deleted from the main personnel database, but some related data may still exist:

**Failed to delete from:** {', '.join(error_messages)}

The personnel cannot access the system, but manual cleanup may be required.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
            )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        management_bot.edit_message_text(
            f"❌ **Error Deleting Personnel**\n\n"
            f"An error occurred while deleting the personnel:\n{str(e)}\n\n"
            f"Please try again or contact system administrator.",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=types.InlineKeyboardMarkup().add(
                types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
            ),
            parse_mode='Markdown'
        )

# Personnel Management Functions
def start_add_personnel(call):
    """Start the process of adding new delivery personnel"""
    management_bot.answer_callback_query(call.id, "➕ Starting personnel addition...")

    text = """
➕ **Add New Delivery Personnel**

Please provide the following information in this format:

```
Name: [Full Name]
Phone: [Phone Number with country code]
Telegram ID: [Telegram User ID]
```

**Example:**
```
Name: John Doe
Phone: +251912345678
Telegram ID: 123456789
```

**Note:**
• Firestore will automatically assign a unique document ID
• Personnel will be initialized with default settings
• Earnings tracking will be automatically set up
• Service areas can be configured later

Reply to this message with the personnel information.
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Set up message handler for personnel data
    management_bot.register_next_step_handler(call.message, process_add_personnel)

def process_add_personnel(message):
    """Process the personnel addition request"""
    try:
        # Parse the personnel data
        lines = message.text.strip().split('\n')
        personnel_data = {}

        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()
                personnel_data[key] = value

        # Validate required fields
        required_fields = ['name', 'phone', 'telegram id']
        missing_fields = [field for field in required_fields if field not in personnel_data]

        if missing_fields:
            management_bot.reply_to(
                message,
                f"❌ **Missing Information**\n\n"
                f"Please provide: {', '.join(missing_fields)}\n\n"
                f"Use the format shown in the previous message.",
                parse_mode='Markdown'
            )
            return

        # Validate input data using validation functions
        telegram_id_str = personnel_data['telegram id']
        name = personnel_data['name']
        phone = personnel_data['phone']

        # Validate Telegram ID using the comprehensive validation function
        # Use the function at line 2146 that returns (bool, int) tuple
        validation_result = None
        try:
            # Convert to int and check if it's a valid Telegram ID
            tid = int(telegram_id_str)
            # Telegram IDs are typically 9-10 digits
            if 100000000 <= tid <= 9999999999:
                validation_result = (True, tid)
            else:
                validation_result = (False, None)
        except (ValueError, TypeError):
            validation_result = (False, None)

        is_valid, telegram_id = validation_result
        if not is_valid:
            management_bot.reply_to(
                message,
                f"❌ **Invalid Telegram ID**\n\n"
                f"Telegram ID must be numeric and 9-10 digits. You provided: {telegram_id_str}\n\n"
                f"Please provide a valid Telegram ID.",
                parse_mode='Markdown'
            )
            return

        # Log the data type conversion for debugging
        logger.info(f"🔄 Telegram ID conversion: '{telegram_id_str}' (str) → {telegram_id} (int)")

        # Validate name
        if not validate_name(name):
            management_bot.reply_to(
                message,
                f"❌ **Invalid Name**\n\n"
                f"Name must be 2-50 characters and contain letters, numbers, spaces, and common punctuation (hyphens, apostrophes, periods, commas). You provided: {name}\n\n"
                f"✅ **Valid examples:** John O'Connor, Mary-Jane, Ahmed Al-Hassan, Driver-01, Dr. Smith\n\n"
                f"Please provide a valid name.",
                parse_mode='Markdown'
            )
            return

        # Validate phone number
        if not validate_phone_number(phone):
            management_bot.reply_to(
                message,
                f"❌ **Invalid Phone Number**\n\n"
                f"Phone number should start with '+' (international) or '0' (local) and be at least 10 characters. You provided: {phone}\n\n"
                f"Please provide a valid phone number.",
                parse_mode='Markdown'
            )
            return

        # Check if Telegram ID already exists
        existing_personnel = get_data("delivery_personnel") or {}
        for pid, pdata in existing_personnel.items():
            # Handle both string and integer comparisons for backward compatibility
            existing_tid = pdata.get('telegram_id')
            if existing_tid and (str(existing_tid) == str(telegram_id) or
                                (isinstance(existing_tid, str) and existing_tid.isdigit() and int(existing_tid) == telegram_id) or
                                existing_tid == telegram_id):
                management_bot.reply_to(
                    message,
                    f"❌ **Telegram ID Already Exists**\n\n"
                    f"A personnel with Telegram ID {telegram_id} already exists.\n\n"
                    f"Personnel: {pdata.get('name', 'Unknown')} (ID: {pid})",
                    parse_mode='Markdown'
                )
                return

        # Create delivery personnel using Firestore auto-ID
        import uuid
        from src.data_models import DeliveryPersonnel, DeliveryPersonnelEarnings

        # Generate unique personnel ID using Firestore-style auto-ID
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"

        # Create personnel object
        personnel = DeliveryPersonnel(personnel_id)
        personnel.name = personnel_data['name']
        personnel.phone_number = phone
        personnel.telegram_id = str(telegram_id)  # Ensure consistent string storage
        personnel.service_areas = ['1', '2', '3', '4', '5']  # All areas - can be customized later
        personnel.vehicle_type = 'motorcycle'  # Default vehicle type
        personnel.max_capacity = 5
        personnel.status = 'available'  # Set to available so they can receive broadcasts
        personnel.is_verified = True  # Auto-verify new personnel for immediate broadcast eligibility

        # Save to Firebase with enhanced data integrity
        personnel_dict = personnel.to_dict()
        existing_personnel[personnel_id] = personnel_dict

        logger.info(f"Saving personnel {personnel_id} to Firebase with enhanced integrity checks")
        success = safe_firebase_set("delivery_personnel", existing_personnel)

        if not success:
            management_bot.reply_to(
                message,
                f"❌ **Database Error**\n\n"
                f"Failed to save personnel to database after multiple attempts. Please try again.",
                parse_mode='Markdown'
            )
            return

        # Verify data integrity immediately after storage
        if not verify_personnel_data_integrity(personnel_id, personnel_dict):
            logger.error(f"Data integrity verification failed for personnel {personnel_id}")

            # Try to fix the issue by re-adding the personnel
            logger.info(f"Attempting to fix data integrity issue for {personnel_id}")

            # Re-add to delivery_personnel collection with direct approach
            try:
                fresh_personnel_data = get_data("delivery_personnel") or {}
                fresh_personnel_data[personnel_id] = personnel_dict

                if set_data("delivery_personnel", fresh_personnel_data):
                    logger.info(f"Successfully re-added personnel {personnel_id} to delivery_personnel")

                    # Verify again
                    if verify_personnel_data_integrity(personnel_id, personnel_dict):
                        logger.info(f"Data integrity verification now passed for {personnel_id}")
                    else:
                        logger.error(f"Data integrity still failing for {personnel_id}")
                        notify_admin_error(f"Critical: Data integrity verification failed for personnel {personnel_id} after retry")
                else:
                    logger.error(f"Failed to re-add personnel {personnel_id} to delivery_personnel")
                    notify_admin_error(f"Critical: Failed to save personnel {personnel_id} to delivery_personnel")

            except Exception as e:
                logger.error(f"Exception during personnel data fix for {personnel_id}: {e}")
                notify_admin_error(f"Exception during personnel data fix for {personnel_id}: {e}")

            # Continue with the process even if verification failed initially

        # Initialize earnings tracking
        from src.utils.earnings_utils import get_or_create_personnel_earnings
        earnings = get_or_create_personnel_earnings(personnel_id)

        # Save earnings to Firebase with enhanced operations
        earnings_data = get_data("delivery_personnel_earnings") or {}
        earnings_data[personnel_id] = earnings.to_dict()
        if not safe_firebase_set("delivery_personnel_earnings", earnings_data):
            logger.error(f"Failed to save earnings data for personnel {personnel_id}")
            notify_admin_error(f"Failed to save earnings data for personnel {personnel_id}")

        # Initialize other data structures
        availability_data = get_data("delivery_personnel_availability") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}
        zones_data = get_data("delivery_personnel_zones") or {}
        performance_data = get_data("delivery_personnel_performance") or {}

        availability_data[personnel_id] = "available"  # Set to available for immediate broadcast eligibility
        capacity_data[personnel_id] = 0
        zones_data[personnel_id] = ['1', '2', '3', '4', '5']  # All areas - can be customized later
        performance_data[personnel_id] = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "average_rating": 5.0,
            "total_distance": 0.0,
            "average_delivery_time": 0.0,
            "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # Save all data structures with enhanced operations and real-time synchronization
        operations = [
            ("delivery_personnel_availability", availability_data),
            ("delivery_personnel_capacity", capacity_data),
            ("delivery_personnel_zones", zones_data),
            ("delivery_personnel_performance", performance_data)
        ]

        failed_operations = []
        for path, data in operations:
            if not safe_firebase_set(path, data):
                failed_operations.append(path)
                logger.error(f"Failed to save {path} for personnel {personnel_id}")

        if failed_operations:
            notify_admin_error(f"Failed to save data structures for personnel {personnel_id}: {failed_operations}")

        # Add to authorized delivery personnel for immediate delivery bot access
        admin_id = message.from_user.id
        auth_success = add_authorized_delivery_personnel(telegram_id, name, admin_id)

        # Clear delivery bot authorization cache to force refresh
        try:
            from src.bots.delivery_bot import clear_authorization_cache, get_authorized_delivery_ids_from_firebase, get_personnel_by_telegram_id
            clear_authorization_cache()
            logger.info("Delivery bot authorization cache cleared after personnel addition")

            # Force immediate refresh to verify the new personnel is included
            fresh_authorized_ids = get_authorized_delivery_ids_from_firebase()

            # Enhanced debugging for authorization verification
            logger.info(f"🔍 Authorization verification for Telegram ID {telegram_id} (type: {type(telegram_id)})")
            logger.info(f"📋 Fresh authorized IDs: {fresh_authorized_ids}")
            logger.info(f"🔍 ID types in list: {[type(id_val) for id_val in fresh_authorized_ids[:3]]}")  # Show first 3 types
            logger.info(f"🎯 Checking: {telegram_id} in {fresh_authorized_ids}")

            if telegram_id in fresh_authorized_ids:
                logger.info(f"✅ Verified: Telegram ID {telegram_id} is now in authorized list")
            else:
                logger.error(f"❌ ERROR: Telegram ID {telegram_id} NOT found in refreshed authorized list")
                logger.error(f"   Current authorized IDs: {fresh_authorized_ids}")
                logger.error(f"   Telegram ID type: {type(telegram_id)}, value: {telegram_id}")
                logger.error(f"   List contains integers: {all(isinstance(x, int) for x in fresh_authorized_ids)}")
                logger.error(f"   Manual check results:")
                for auth_id in fresh_authorized_ids:
                    logger.error(f"     {telegram_id} == {auth_id}: {telegram_id == auth_id} (types: {type(telegram_id)} vs {type(auth_id)})")
                notify_admin_error(f"Personnel {personnel_id} authorization verification failed")

            # Also verify personnel lookup works
            personnel_lookup = get_personnel_by_telegram_id(telegram_id)
            if personnel_lookup:
                logger.info(f"✅ Verified: Personnel lookup successful for Telegram ID {telegram_id}")
                logger.info(f"   Found personnel: {personnel_lookup.name} ({personnel_lookup.personnel_id})")
            else:
                logger.error(f"❌ ERROR: Personnel lookup failed for Telegram ID {telegram_id}")
                notify_admin_error(f"Personnel lookup verification failed for {personnel_id} (Telegram ID: {telegram_id})")

        except Exception as e:
            logger.warning(f"Could not verify delivery bot integration: {e}")
            notify_admin_error(f"Failed to verify delivery bot integration for personnel {personnel_id}")

        if not auth_success:
            logger.error(f"Failed to add personnel {personnel_id} to authorized delivery list")
            notify_admin_error(f"Personnel {personnel_id} added but authorization failed")

        # Success message with authorization status using safe formatting
        auth_status = "✅ Authorized" if auth_success else "❌ Authorization Failed"

        # Create message template with placeholders
        success_template = """✅ **Personnel Added Successfully\\!**

**Details:**
• **Firestore ID:** `{personnel_id}`
• **Name:** {name}
• **Phone:** {phone}
• **Telegram ID:** {telegram_id}
• **Service Areas:** 1 \\(default, can be edited\\)
• **Vehicle:** Motorcycle \\(default, can be edited\\)
• **Max Capacity:** 5 orders

**System Setup:**
• Status: Offline \\(until they log in\\)
• Verification: Pending \\(requires admin approval\\)
• Earnings Tracking: Initialized \\(0\\.00 birr\\)
• Performance Metrics: Initialized
• **Delivery Bot Access:** {auth_status}

**Next Steps:**
1\\. Personnel can now access the delivery bot immediately
2\\. Use 'Edit' function to configure service areas and vehicle
3\\. Admin can verify them in Personnel Management

**✅ Authorization:** Personnel has been automatically authorized for delivery bot access\\."""

        # Safely format the message with escaped content
        success_text, format_success = safe_format_message(
            success_template,
            personnel_id=personnel_id,
            name=personnel_data['name'],
            phone=phone,
            telegram_id=telegram_id,
            auth_status=auth_status
        )

        # Add back button to return to personnel management
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
        )

        # Use safe message sending with proper error handling
        try:
            if format_success:
                # Try MarkdownV2 first
                management_bot.reply_to(
                    message,
                    success_text,
                    reply_markup=keyboard,
                    parse_mode='MarkdownV2'
                )
            else:
                # Fallback to plain text if formatting failed
                management_bot.reply_to(
                    message,
                    success_text,
                    reply_markup=keyboard,
                    parse_mode=None
                )
        except Exception as msg_error:
            logger.error(f"Failed to send success message: {msg_error}")
            # Final fallback message
            fallback_text = f"✅ Personnel Added Successfully!\n\nName: {personnel_data['name']}\nTelegram ID: {telegram_id}\nAuthorization: {'Success' if auth_success else 'Failed'}"
            management_bot.reply_to(
                message,
                fallback_text,
                reply_markup=keyboard,
                parse_mode=None
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error Adding Personnel**\n\n"
            f"An error occurred: {str(e)}\n\n"
            f"Please try again or contact system administrator.",
            parse_mode='Markdown'
        )

def start_remove_personnel(call):
    """Start the process of removing delivery personnel"""
    # Get all personnel
    personnel_data = get_data("delivery_personnel") or {}

    if not personnel_data:
        management_bot.edit_message_text(
            "❌ **No Personnel Found**\n\n"
            "There are no delivery personnel in the system to remove.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )
        return

    # Create keyboard with personnel list
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for personnel_id, person in personnel_data.items():
        name = person.get('name', 'Unknown')
        status = person.get('status', 'unknown')
        button_text = f"🗑️ {name} ({status})"
        keyboard.add(
            types.InlineKeyboardButton(
                button_text,
                callback_data=f"remove_personnel_{personnel_id}"
            )
        )

    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
    )

    text = """
➖ **Remove Delivery Personnel**

Select a personnel member to remove from the system:

⚠️ **Warning:** This action cannot be undone!
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_list(call):
    """Show comprehensive list of all delivery personnel"""
    management_bot.answer_callback_query(call.id, "📋 Loading personnel list...")

    # Get all personnel-related data
    personnel_data = get_data("delivery_personnel") or {}
    availability_data = get_data("delivery_personnel_availability") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}

    if not personnel_data:
        text = """
📋 **Personnel List**

❌ **No Personnel Found**

There are no delivery personnel registered in the system.

**Available Actions:**
• Add new personnel
• Import personnel data
• Check system configuration
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("➕ Add Personnel", callback_data="pers_add")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Management", callback_data="mgmt_personnel")
        )
    else:
        # Calculate statistics
        total_personnel = len(personnel_data)
        verified_count = sum(1 for p in personnel_data.values() if p.get('is_verified', False))
        status_counts = {"available": 0, "busy": 0, "offline": 0, "inactive": 0}

        for person in personnel_data.values():
            status = person.get('status', 'offline')
            status_counts[status] = status_counts.get(status, 0) + 1

        text = f"""
📋 **Personnel List**

**📊 Summary:**
• Total: {total_personnel}
• Verified: {verified_count}/{total_personnel}
• Available: {status_counts.get('available', 0)}
• Busy: {status_counts.get('busy', 0)}
• Offline: {status_counts.get('offline', 0)}

**👥 Personnel Details:**
        """

        # Sort personnel by status and name
        sorted_personnel = sorted(
            personnel_data.items(),
            key=lambda x: (
                x[1].get('status', 'offline'),
                not x[1].get('is_verified', False),
                x[1].get('name', 'Unknown')
            )
        )

        for personnel_id, person in sorted_personnel:
            name = person.get('name', 'Unknown')
            phone = person.get('phone_number', 'N/A')
            status = person.get('status', 'offline')
            is_verified = person.get('is_verified', False)
            areas = person.get('service_areas', [])
            vehicle = person.get('vehicle_type', 'N/A')
            current_orders = capacity_data.get(personnel_id, 0)
            max_capacity = person.get('max_capacity', 5)

            # Status and verification emojis
            status_emoji = {
                'available': '✅',
                'busy': '🔄',
                'offline': '⭕',
                'inactive': '❌'
            }.get(status, '❓')

            verified_emoji = '✅' if is_verified else '⚠️'

            text += f"""
**{status_emoji}{verified_emoji} {name}**
• ID: `{personnel_id[:12]}...`
• Phone: {phone}
• Status: {status.title()}
• Orders: {current_orders}/{max_capacity}
• Vehicle: {vehicle.title()}
• Areas: {', '.join(areas[:3]) if areas else 'None'}{'...' if len(areas) > 3 else ''}
---
            """

        # Add action buttons
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("➕ Add New", callback_data="pers_add"),
            types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_list")
        )
        keyboard.add(
            types.InlineKeyboardButton("📊 Performance Report", callback_data="pers_performance"),
            types.InlineKeyboardButton("🔧 Bulk Actions", callback_data="pers_bulk")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Management", callback_data="mgmt_personnel")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def start_personnel_search(call):
    """Start personnel search functionality"""
    management_bot.answer_callback_query(call.id, "🔍 Search feature coming soon!")

def show_personnel_performance(call):
    """Show comprehensive personnel performance analytics"""
    management_bot.answer_callback_query(call.id, "📊 Loading performance analytics...")

    # Get all performance-related data
    personnel_data = get_data("delivery_personnel") or {}
    performance_data = get_data("delivery_personnel_performance") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}

    if not personnel_data:
        text = """
📊 **Performance Analytics**

❌ **No Personnel Data**

There are no delivery personnel to analyze.
        """
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
        )
    else:
        # Calculate overall statistics
        total_personnel = len(personnel_data)
        total_deliveries = 0
        total_successful = 0
        total_ratings = []
        active_personnel = 0
        verified_personnel = 0

        performance_list = []

        for personnel_id, person in personnel_data.items():
            name = person.get('name', 'Unknown')
            status = person.get('status', 'offline')
            is_verified = person.get('is_verified', False)

            # Get performance data
            perf = performance_data.get(personnel_id, {})
            deliveries = perf.get('total_deliveries', person.get('total_deliveries', 0))
            successful = perf.get('successful_deliveries', person.get('successful_deliveries', 0))
            rating = perf.get('average_rating', person.get('rating', 5.0))
            current_orders = capacity_data.get(personnel_id, 0)

            # Calculate success rate
            success_rate = (successful / deliveries * 100) if deliveries > 0 else 0

            # Add to totals
            total_deliveries += deliveries
            total_successful += successful
            if rating > 0:
                total_ratings.append(rating)
            if status in ['available', 'busy']:
                active_personnel += 1
            if is_verified:
                verified_personnel += 1

            performance_list.append({
                'name': name,
                'deliveries': deliveries,
                'successful': successful,
                'success_rate': success_rate,
                'rating': rating,
                'current_orders': current_orders,
                'status': status,
                'verified': is_verified
            })

        # Calculate overall metrics
        overall_success_rate = (total_successful / total_deliveries * 100) if total_deliveries > 0 else 0
        average_rating = sum(total_ratings) / len(total_ratings) if total_ratings else 0

        # Sort by performance (deliveries and success rate)
        performance_list.sort(key=lambda x: (x['deliveries'], x['success_rate']), reverse=True)

        text = f"""
📊 **Performance Analytics**

**🎯 Overall Metrics:**
• Total Personnel: {total_personnel}
• Active: {active_personnel} | Verified: {verified_personnel}
• Total Deliveries: {total_deliveries}
• Success Rate: {overall_success_rate:.1f}%
• Average Rating: {average_rating:.1f}/5.0

**🏆 Top Performers:**
        """

        # Show top 5 performers
        for i, performer in enumerate(performance_list[:5], 1):
            status_emoji = {
                'available': '✅',
                'busy': '🔄',
                'offline': '⭕',
                'inactive': '❌'
            }.get(performer['status'], '❓')

            verified_emoji = '✅' if performer['verified'] else '⚠️'

            text += f"""
**{i}. {status_emoji}{verified_emoji} {performer['name']}**
• Deliveries: {performer['deliveries']} | Success: {performer['success_rate']:.1f}%
• Rating: {performer['rating']:.1f}/5.0 | Current: {performer['current_orders']} orders
            """

        if len(performance_list) > 5:
            text += f"\n... and {len(performance_list) - 5} more personnel"

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("📈 Detailed Report", callback_data="perf_detailed"),
            types.InlineKeyboardButton("📊 Export Data", callback_data="perf_export")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_performance"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_personnel")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_status_management(call):
    """Show personnel status management interface"""
    management_bot.answer_callback_query(call.id, "🔧 Loading status management...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}

    if not personnel_data:
        text = """
🔧 **Status Management**

❌ **No Personnel Found**

There are no delivery personnel to manage.
        """
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
        )
    else:
        # Group by status
        status_groups = {"available": [], "busy": [], "offline": [], "inactive": []}

        for personnel_id, person in personnel_data.items():
            name = person.get('name', 'Unknown')
            status = person.get('status', 'offline')
            is_verified = person.get('is_verified', False)

            status_groups[status].append({
                'id': personnel_id,
                'name': name,
                'verified': is_verified
            })

        text = f"""
🔧 **Personnel Status Management**

**📊 Current Status Distribution:**
• Available: {len(status_groups['available'])}
• Busy: {len(status_groups['busy'])}
• Offline: {len(status_groups['offline'])}
• Inactive: {len(status_groups['inactive'])}

**🔧 Bulk Actions:**
        """

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("✅ Activate All Offline", callback_data="bulk_activate"),
            types.InlineKeyboardButton("⭕ Set All Offline", callback_data="bulk_offline")
        )
        keyboard.add(
            types.InlineKeyboardButton("✅ Verify All Pending", callback_data="bulk_verify"),
            types.InlineKeyboardButton("⚠️ Unverify All", callback_data="bulk_unverify")
        )
        keyboard.add(
            types.InlineKeyboardButton("📋 Individual Status", callback_data="status_individual"),
            types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_status")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def handle_personnel_removal(call):
    """Handle personnel removal confirmation"""
    personnel_id = call.data.replace("remove_personnel_", "")

    # Get personnel details
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    status = person.get('status', 'unknown')

    # Create confirmation keyboard
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✅ Confirm Remove", callback_data=f"confirm_remove_{personnel_id}"),
        types.InlineKeyboardButton("❌ Cancel", callback_data="pers_remove")
    )

    text = f"""
⚠️ **Confirm Personnel Removal**

**Personnel Details:**
• **Name:** {name}
• **Phone:** {phone}
• **Status:** {status.title()}
• **ID:** `{personnel_id}`

**Warning:** This action will:
• Remove the personnel from the system
• Clear all their assignments
• Delete their performance history
• This action cannot be undone!

Are you sure you want to proceed?
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def confirm_personnel_removal(call):
    """Confirm and execute personnel removal"""
    personnel_id = call.data.replace("confirm_remove_", "")

    try:
        # Get personnel details before removal
        personnel_data = get_data("delivery_personnel") or {}
        person = personnel_data.get(personnel_id)

        if not person:
            management_bot.answer_callback_query(
                call.id,
                "❌ Personnel not found!",
                show_alert=True
            )
            return

        name = person.get('name', 'Unknown')
        telegram_id = person.get('telegram_id')

        # Remove personnel using utility function
        success = remove_delivery_personnel(personnel_id)

        # Remove from authorized delivery personnel
        auth_removal_success = False
        if telegram_id:
            admin_id = call.from_user.id
            auth_removal_success = remove_authorized_delivery_personnel(telegram_id, admin_id)

            # Clear delivery bot authorization cache to force refresh
            try:
                from src.bots.delivery_bot import clear_authorization_cache
                clear_authorization_cache()
                logger.info("Delivery bot authorization cache cleared after personnel removal")
            except Exception as e:
                logger.warning(f"Could not clear delivery bot authorization cache: {e}")

            if not auth_removal_success:
                logger.error(f"Failed to remove personnel {personnel_id} from authorized delivery list")

        if success:
            auth_status = "✅ Removed" if auth_removal_success else "⚠️ Manual removal needed"
            text = f"""
✅ **Personnel Removed Successfully**

**{name}** has been removed from the system.

All associated data including:
• Personnel record
• Assignment history
• Performance metrics
• Availability status
• **Delivery Bot Access:** {auth_status}

Has been permanently deleted.

**✅ Authorization:** Personnel delivery bot access has been revoked immediately.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
            )

            management_bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

        else:
            management_bot.answer_callback_query(
                call.id,
                "❌ Failed to remove personnel. Please try again.",
                show_alert=True
            )

    except Exception as e:
        management_bot.answer_callback_query(
            call.id,
            f"❌ Error: {str(e)}",
            show_alert=True
        )

# Analytics Counter Integration Functions
def update_order_analytics_counters(order_status: str, operation: str = 'increment'):
    """
    Update analytics counters when order status changes

    Args:
        order_status: 'complete', 'incomplete', or 'issue'
        operation: 'increment' or 'decrement'
    """
    try:
        from src.utils.analytics_counter_system import update_analytics_counter

        # Map order status to counter type
        counter_type_map = {
            'complete': 'complete_orders',
            'incomplete': 'incomplete_orders',
            'issue': 'issue_orders'
        }

        counter_type = counter_type_map.get(order_status)
        if not counter_type:
            logger.warning(f"Unknown order status for analytics: {order_status}")
            return False

        # Update all periods (daily, weekly, monthly)
        success = True
        for period in ['daily', 'weekly', 'monthly']:
            if operation == 'increment':
                period_success = update_analytics_counter(counter_type, period)
                success = success and period_success
            # Note: decrement operation would need separate implementation
            # if needed in the future

        if success:
            logger.debug(f"📊 Analytics counters updated: {order_status} ({operation})")
        else:
            logger.error(f"Failed to update analytics counters: {order_status} ({operation})")

        return success

    except Exception as e:
        logger.error(f"Error updating order analytics counters: {e}")
        return False


def get_enhanced_analytics_data(period_type: str) -> Dict[str, Any]:
    """
    Get enhanced analytics data combining real-time order data with counter system

    Args:
        period_type: 'daily', 'weekly', or 'monthly'
    """
    try:
        from src.utils.analytics_counter_system import get_analytics_counter_summary

        # Get counter data
        counter_summary = get_analytics_counter_summary()
        period_counters = counter_summary.get('periods', {}).get(period_type, {})

        # Get real-time order data for verification
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Filter orders for the specific period
        filtered_orders = filter_orders_by_time_period_with_resets(
            {'completed_orders': completed_orders_data, 'confirmed_orders': confirmed_orders_data},
            period_type
        )

        # Categorize filtered orders
        categorized_orders = categorize_orders_by_status(
            filtered_orders.get('completed_orders', {}),
            filtered_orders.get('confirmed_orders', {}),
            assignments_data
        )

        # Calculate real-time statistics
        real_time_stats = calculate_category_percentages(categorized_orders)

        # Combine counter data with real-time verification
        enhanced_data = {
            'period_type': period_type,
            'period_start': period_counters.get('period_start', 'N/A'),
            'counters': {
                'complete_orders': period_counters.get('complete_orders', 0),
                'incomplete_orders': period_counters.get('incomplete_orders', 0),
                'issue_orders': period_counters.get('issue_orders', 0),
                'total_orders': (
                    period_counters.get('complete_orders', 0) +
                    period_counters.get('incomplete_orders', 0) +
                    period_counters.get('issue_orders', 0)
                )
            },
            'real_time_verification': {
                'complete_count': real_time_stats.get('complete_count', 0),
                'incomplete_count': real_time_stats.get('incomplete_count', 0),
                'issue_count': real_time_stats.get('issue_count', 0),
                'total_count': real_time_stats.get('total_count', 0)
            },
            'categorized_orders': categorized_orders,
            'last_updated': period_counters.get('last_updated', 'N/A')
        }

        return enhanced_data

    except Exception as e:
        logger.error(f"Error getting enhanced analytics data for {period_type}: {e}")
        return {}


# Analytics Functions
def show_daily_analytics(call):
    """Show optimized daily analytics summary with three-category reporting and real-time data"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading daily analytics...")

        today = datetime.now().strftime("%Y-%m-%d")

        # Get and validate all order data (using same method as working all-time analytics)
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")
        personnel_data = validate_analytics_data(get_data("delivery_personnel"), "personnel")

        # Filter orders for daily analytics with correct time-based logic
        today_completed_orders = {}
        today_confirmed_orders = {}

        # For completed orders: only include orders completed TODAY
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at and completed_at.startswith(today):
                today_completed_orders[order_id] = order

        # For confirmed orders: only include orders CREATED TODAY (filter by original creation date)
        # This ensures incomplete orders count respects the time interval filter
        for order_id, order in confirmed_orders_data.items():
            created_at = order.get('created_at', '')
            if created_at and created_at.startswith(today):
                today_confirmed_orders[order_id] = order

        # Categorize today's orders by status
        categorized_orders = categorize_orders_by_status(
            today_completed_orders,
            today_confirmed_orders,
            assignments_data
        )

        # Calculate category percentages
        category_stats = calculate_category_percentages(categorized_orders)

        # Calculate order metrics with safe operations
        total_orders_today = category_stats['total_count']
        complete_orders_count = category_stats['complete_count']
        incomplete_orders_count = category_stats['incomplete_count']
        issue_orders_count = category_stats['issue_count']

        # Calculate revenue analytics using new cost accounting system
        revenue_breakdown = calculate_revenue_breakdown(categorized_orders['complete'])

        food_revenue = revenue_breakdown['food_revenue']
        total_cash_revenue = revenue_breakdown['total_cash_revenue']
        total_points_used = revenue_breakdown['total_points_used']
        delivery_fees_cash = revenue_breakdown['delivery_fees_cash']
        delivery_fees_points = revenue_breakdown['delivery_fees_points']
        cash_revenue = revenue_breakdown['cash_revenue']
        point_payment_costs = revenue_breakdown['point_payment_costs']

        # Calculate net profit using cost accounting (can be negative)
        company_profit = revenue_breakdown['company_profit']  # cash_revenue - point_payment_costs
        personnel_earnings = revenue_breakdown['personnel_earnings']

        # Total revenue includes both cash and point value for display
        total_revenue = total_cash_revenue + total_points_used
        total_profit = company_profit  # Net company profit (can be negative)
        profit_margin = safe_calculate_percentage(total_profit, total_cash_revenue, 0)

        # Get delivery personnel activity with safe counting
        active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])
        busy_personnel = len([p for p in personnel_data.values() if p.get('status') == 'busy'])

        # Safely calculate averages
        avg_order_value = (total_revenue / complete_orders_count) if complete_orders_count > 0 else 0
        completion_rate = safe_calculate_percentage(complete_orders_count, total_orders_today, 0)

        # Check for zero-data scenario
        if total_orders_today == 0:
            text = f"""📅 **Daily Analytics - {today}**

🆕 **No Orders Today**

**Current Status:**
• No orders processed yet today
• System ready for new orders
• Personnel available for assignments

**System Status:**
👥 **Staff:** {active_personnel} active | {busy_personnel} busy | {len(personnel_data)} total

**Note:** Analytics will update as orders are processed throughout the day."""
        else:
            # Create optimized, concise text with three-category breakdown
            text = f"""📅 **Daily Analytics - {today}**

📊 **Order Categories:**
• ✅ Complete: {complete_orders_count} ({category_stats['complete_percentage']:.1f}%)
• ⏳ Incomplete: {incomplete_orders_count} ({category_stats['incomplete_percentage']:.1f}%)
• ⚠️ Issues: {issue_orders_count} ({category_stats['issue_percentage']:.1f}%)
• **Total: {total_orders_today} orders**

📈 **Completion Rate:** {completion_rate:.1f}%

💰 **Revenue (Complete Orders Only):**
• Food: {food_revenue:.0f} birr (cash)
• Delivery (Cash): {delivery_fees_cash:.0f} birr
• Delivery (Points): {delivery_fees_points:.0f} points
• **Total Cash: {total_cash_revenue:.0f} birr**
• **Points Used: {total_points_used:.0f} points**

{"📈" if total_profit >= 0 else "📉"} **Profit/Loss Analysis:**
• Cash Revenue: {cash_revenue:.0f} birr (50% of cash delivery fees)
• Point Payment Costs: {point_payment_costs:.0f} birr (50% of point delivery fees)
• **Net Company Profit: {company_profit:.0f} birr** ({profit_margin:.1f}% of cash revenue)
• Personnel Earnings: {personnel_earnings:.0f} birr (50% of all delivery fees)

👥 **Staff:** {active_personnel} active | {busy_personnel} busy | {len(personnel_data)} total
💵 **Avg Order:** {avg_order_value:.0f} birr"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_daily"),
            types.InlineKeyboardButton("📊 Details", callback_data="daily_details")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe edit message function
        if not safe_edit_message(call, text, keyboard):
            # Fallback error handling
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        handle_analytics_error(call, str(e), "daily_analytics")

def show_weekly_analytics(call):
    """Show optimized weekly analytics summary with detailed order status categorization"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading weekly analytics...")

        # Calculate week range (Monday to Sunday)
        today = datetime.now()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)

        week_range = f"{week_start.strftime('%m/%d')} - {week_end.strftime('%m/%d')}"

        # Get and validate data
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Filter orders for weekly analytics with correct in-progress logic
        week_completed_orders = {}
        week_confirmed_orders = {}

        # For completed orders: only include orders completed THIS WEEK
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if week_start.date() <= order_date.date() <= week_end.date():
                        week_completed_orders[order_id] = order
                except:
                    continue

        # For confirmed orders: only include orders CREATED THIS WEEK (filter by original creation date)
        # This ensures incomplete orders count respects the time interval filter
        for order_id, order in confirmed_orders_data.items():
            created_at = order.get('created_at', '')
            if created_at:
                try:
                    order_date = datetime.strptime(created_at[:10], '%Y-%m-%d')
                    if week_start.date() <= order_date.date() <= week_end.date():
                        week_confirmed_orders[order_id] = order
                except:
                    continue

        # Categorize week's orders by status
        categorized_orders = categorize_orders_by_status(
            week_completed_orders,
            week_confirmed_orders,
            assignments_data
        )

        # Calculate category statistics
        category_stats = calculate_category_percentages(categorized_orders)

        # Calculate metrics with detailed categorization
        total_orders = category_stats['total_count']
        complete_orders_count = category_stats['complete_count']
        incomplete_orders_count = category_stats['incomplete_count']
        issue_orders_count = category_stats['issue_count']

        # Calculate revenue analytics using new cost accounting system
        revenue_breakdown = calculate_revenue_breakdown(categorized_orders['complete'])

        food_revenue = revenue_breakdown['food_revenue']
        total_cash_revenue = revenue_breakdown['total_cash_revenue']
        total_points_used = revenue_breakdown['total_points_used']
        delivery_fees_cash = revenue_breakdown['delivery_fees_cash']
        delivery_fees_points = revenue_breakdown['delivery_fees_points']
        cash_revenue = revenue_breakdown['cash_revenue']
        point_payment_costs = revenue_breakdown['point_payment_costs']

        # Calculate net profit using cost accounting (can be negative)
        company_profit = revenue_breakdown['company_profit']
        personnel_earnings = revenue_breakdown['personnel_earnings']

        total_revenue = total_cash_revenue + total_points_used
        total_profit = company_profit
        profit_margin = safe_calculate_percentage(total_profit, total_cash_revenue, 0)

        # Create optimized daily breakdown (top 3 days only)
        daily_counts = {}
        for i in range(7):
            day = week_start + timedelta(days=i)
            day_str = day.strftime('%Y-%m-%d')
            day_name = day.strftime('%a')

            day_completed = [o for o in week_completed_orders.values() if o.get('completed_at', '').startswith(day_str)]
            day_confirmed = [o for o in week_confirmed_orders.values() if o.get('confirmed_at', '').startswith(day_str)]

            daily_counts[day_name] = len(day_completed) + len(day_confirmed)

        # Show top 3 days only
        top_days = sorted(daily_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        daily_breakdown = ' | '.join([f"{day}: {count}" for day, count in top_days])

        # Calculate completion rate
        completion_rate = safe_calculate_percentage(complete_orders_count, total_orders, 0)

        # Create detailed text with order status breakdown
        text = f"""📊 **Weekly Analytics** ({week_range})

📈 **Order Summary:** {total_orders} total orders
• ✅ Complete: {complete_orders_count} ({category_stats['complete_percentage']:.1f}%)
• 🔄 In Progress: {incomplete_orders_count} ({category_stats['incomplete_percentage']:.1f}%)
• ⚠️ Issues: {issue_orders_count} ({category_stats['issue_percentage']:.1f}%)

📊 **Performance:** {completion_rate:.1f}% success rate
• Daily Average: {total_orders/7:.1f} orders

💰 **Revenue:**
• Food: {food_revenue:.0f} birr (cash)
• Delivery (Cash): {delivery_fees_cash:.0f} birr
• Delivery (Points): {delivery_fees_points:.0f} points
• **Total Cash: {total_cash_revenue:.0f} birr**

{"📈" if total_profit >= 0 else "📉"} **Profit/Loss:** {total_profit:.0f} birr ({profit_margin:.1f}% of cash)
• Cash Revenue: {cash_revenue:.0f} birr | Point Costs: {point_payment_costs:.0f} birr
• Personnel: {personnel_earnings:.0f} birr | **Net Company: {company_profit:.0f} birr**

📅 **Top Days:** {daily_breakdown}"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_weekly"),
            types.InlineKeyboardButton("📊 Details", callback_data="weekly_details")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe edit message function
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        handle_analytics_error(call, str(e), "weekly_analytics")

def show_monthly_analytics(call):
    """Show comprehensive monthly analytics summary with enhanced counter system and error handling"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading monthly analytics...")

        # Force refresh analytics data to ensure we get the latest from Firebase
        logger.info("🔄 Monthly analytics: Forcing fresh data refresh from Firebase")
        refresh_analytics_data()

        # Initialize default values for month calculation
        today = datetime.now()
        month_start = today.replace(day=1)
        if today.month == 12:
            month_end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        # Get and validate data (always needed for weekly breakdown)
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Debug: Log what we're getting from Firebase
        logger.info(f"🔍 Monthly analytics - Raw Firebase data:")
        logger.info(f"   • completed_orders collection: {len(completed_orders_data)} records")
        logger.info(f"   • confirmed_orders collection: {len(confirmed_orders_data)} records")
        logger.info(f"   • assignments collection: {len(assignments_data)} records")
        if completed_orders_data:
            logger.info(f"   • Sample completed order IDs: {list(completed_orders_data.keys())[:3]}")
        if confirmed_orders_data:
            logger.info(f"   • Sample confirmed order IDs: {list(confirmed_orders_data.keys())[:3]}")

        # Initialize month order collections (always needed)
        month_completed_orders = {}
        month_confirmed_orders = {}

        # FORCE REAL-TIME DATA: Always use traditional method to ensure accurate, up-to-date data
        # This bypasses potentially stale counter data and ensures we get current Firebase state
        use_counter_data = False
        logger.info("🔄 Monthly analytics: Using real-time Firebase data (bypassing counters for accuracy)")

        if not use_counter_data:
            # Fallback to traditional method with improved error handling
            month_range = f"{month_start.strftime('%Y-%m-%d')} to {month_end.strftime('%Y-%m-%d')}"

            # Filter orders for monthly analytics with improved error handling
            # For completed orders: only include orders completed THIS MONTH
            for order_id, order in completed_orders_data.items():
                completed_at = order.get('completed_at', '')
                if completed_at and len(completed_at) >= 10:  # Ensure we have at least YYYY-MM-DD
                    try:
                        order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                        if month_start.date() <= order_date.date() <= month_end.date():
                            month_completed_orders[order_id] = order
                    except ValueError as e:
                        logger.warning(f"Invalid date format in completed_at for order {order_id}: '{completed_at}' - {e}")
                        continue
                    except Exception as e:
                        logger.error(f"Unexpected error parsing date for order {order_id}: {e}")
                        continue

            # For confirmed orders: only include orders CREATED THIS MONTH (filter by original creation date)
            # This ensures incomplete orders count respects the time interval filter
            for order_id, order in confirmed_orders_data.items():
                created_at = order.get('created_at', '')
                if created_at and len(created_at) >= 10:  # Ensure we have at least YYYY-MM-DD
                    try:
                        order_date = datetime.strptime(created_at[:10], '%Y-%m-%d')
                        if month_start.date() <= order_date.date() <= month_end.date():
                            month_confirmed_orders[order_id] = order
                    except ValueError as e:
                        logger.warning(f"Invalid date format in created_at for order {order_id}: '{created_at}' - {e}")
                        continue
                    except Exception as e:
                        logger.error(f"Unexpected error parsing created_at date for order {order_id}: {e}")
                        continue

            # Categorize month's orders by status
            categorized_orders = categorize_orders_by_status(
                month_completed_orders,
                month_confirmed_orders,
                assignments_data
            )

            # Calculate category statistics
            category_stats = calculate_category_percentages(categorized_orders)
            logger.info(f"📊 Monthly analytics - Real-time data summary:")
            logger.info(f"   • Completed orders found: {len(month_completed_orders)}")
            logger.info(f"   • Confirmed orders found: {len(month_confirmed_orders)}")
            logger.info(f"   • Total Firebase completed_orders: {len(completed_orders_data)}")
            logger.info(f"   • Total Firebase confirmed_orders: {len(confirmed_orders_data)}")
            logger.info(f"   • Category stats: {category_stats}")
            logger.info(f"   • Month range: {month_range}")

        # Calculate metrics with detailed categorization
        total_orders = category_stats['total_count']
        complete_orders_count = category_stats['complete_count']
        incomplete_orders_count = category_stats['incomplete_count']
        issue_orders_count = category_stats['issue_count']

        # Check for zero-data scenario (like daily analytics)
        if total_orders == 0:
            # Get personnel data for system status
            personnel_data = validate_analytics_data(get_data("delivery_personnel"), "personnel")
            active_personnel = sum(1 for p in personnel_data.values() if p.get('status') == 'available')
            busy_personnel = sum(1 for p in personnel_data.values() if p.get('status') == 'busy')

            month_range = f"{month_start.strftime('%Y-%m-%d')} to {month_end.strftime('%Y-%m-%d')}"

            text = f"""📈 **Monthly Analytics - {month_range}**

🆕 **No Orders This Month**

**Current Status:**
• No orders processed yet this month
• System ready for new orders
• Personnel available for assignments

**System Status:**
👥 **Staff:** {active_personnel} available | {busy_personnel} busy | {len(personnel_data)} total

**Note:** Analytics will update as orders are processed throughout the month.

🕐 **Last Updated:** {datetime.now().strftime('%H:%M:%S')}"""

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_monthly"),
                types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
            )

            # Use safe message editing with fallback mechanisms
            if not safe_edit_message(call, text, keyboard):
                management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)
            return

        # Calculate revenue analytics using new cost accounting system
        if use_counter_data:
            # When using counter data, we need to get actual completed orders for revenue calculation
            # Use traditional method to get the actual order data for revenue calculations
            month_completed_orders_for_revenue = {}
            for order_id, order in completed_orders_data.items():
                completed_at = order.get('completed_at', '')
                if completed_at and len(completed_at) >= 10:
                    try:
                        order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                        if month_start.date() <= order_date.date() <= month_end.date():
                            month_completed_orders_for_revenue[order_id] = order
                    except (ValueError, TypeError):
                        continue

            # Create a temporary categorized orders structure for revenue calculation
            temp_categorized = categorize_orders_by_status(
                month_completed_orders_for_revenue,
                {},  # Empty confirmed orders for revenue calculation
                assignments_data
            )
            revenue_breakdown = calculate_revenue_breakdown(temp_categorized['complete'])
        else:
            revenue_breakdown = calculate_revenue_breakdown(categorized_orders['complete'])

        food_revenue = revenue_breakdown['food_revenue']
        total_cash_revenue = revenue_breakdown['total_cash_revenue']
        total_points_used = revenue_breakdown['total_points_used']
        delivery_fees_cash = revenue_breakdown['delivery_fees_cash']
        delivery_fees_points = revenue_breakdown['delivery_fees_points']
        cash_revenue = revenue_breakdown['cash_revenue']
        point_payment_costs = revenue_breakdown['point_payment_costs']

        # Calculate net profit using cost accounting (can be negative)
        company_profit = revenue_breakdown['company_profit']
        personnel_earnings = revenue_breakdown['personnel_earnings']

        total_revenue = total_cash_revenue + total_points_used
        total_profit = company_profit
        profit_margin = safe_calculate_percentage(total_profit, total_cash_revenue, 0)

        # Weekly breakdown for the month
        weekly_data = {}
        current_week_start = month_start
        week_num = 1

        while current_week_start <= month_end:
            week_end_date = min(current_week_start + timedelta(days=6), month_end)

            week_completed = []
            for o in month_completed_orders.values():
                completed_at = o.get('completed_at', '')
                if completed_at and len(completed_at) >= 10:
                    try:
                        order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                        if current_week_start.date() <= order_date.date() <= week_end_date.date():
                            week_completed.append(o)
                    except (ValueError, TypeError):
                        continue

            week_confirmed = []
            for o in month_confirmed_orders.values():
                confirmed_at = o.get('confirmed_at', '')
                if confirmed_at and len(confirmed_at) >= 10:
                    try:
                        order_date = datetime.strptime(confirmed_at[:10], '%Y-%m-%d')
                        if current_week_start.date() <= order_date.date() <= week_end_date.date():
                            week_confirmed.append(o)
                    except (ValueError, TypeError):
                        continue

            weekly_data[f"Week {week_num}"] = len(week_completed) + len(week_confirmed)
            current_week_start += timedelta(days=7)
            week_num += 1

        weekly_breakdown = '\n'.join([f"• {week}: {count} orders" for week, count in weekly_data.items()])

        # Calculate days in month for averages
        days_in_month = (month_end - month_start).days + 1

        # Format decimal numbers for monthly analytics without escaping decimal points
        food_revenue_str = format_number(food_revenue, 2)
        total_cash_revenue_str = format_number(total_cash_revenue, 2)
        delivery_fees_cash_str = format_number(delivery_fees_cash, 2)
        delivery_fees_points_str = format_number(delivery_fees_points, 0)
        total_points_used_str = format_number(total_points_used, 0)
        cash_revenue_str = format_number(cash_revenue, 2)
        point_payment_costs_str = format_number(point_payment_costs, 2)
        total_profit_str = format_number(total_profit, 2)
        profit_margin_str = format_number(profit_margin, 1)
        personnel_earnings_str = format_number(personnel_earnings, 2)
        company_profit_str = format_number(company_profit, 2)
        daily_avg_str = format_number((total_orders/days_in_month), 1)
        completion_rate_str = format_number((complete_orders_count/total_orders*100) if total_orders > 0 else 0, 1)
        avg_cash_revenue_day_str = format_number((total_cash_revenue/days_in_month), 2)

        text = f"""
📈 **Monthly Analytics**
**Period:** {month_range}

**📊 Order Summary:** {total_orders} total orders
• ✅ Complete: {complete_orders_count} ({category_stats['complete_percentage']:.1f}%)
• 🔄 In Progress: {incomplete_orders_count} ({category_stats['incomplete_percentage']:.1f}%)
• ⚠️ Issues: {issue_orders_count} ({category_stats['issue_percentage']:.1f}%)

**📊 Performance:**
• Success Rate: {completion_rate_str}%
• Daily Average: {daily_avg_str} orders

**💰 Revenue Analytics:**
• Food Revenue: {food_revenue_str} birr (cash)
• Delivery Fees (Cash): {delivery_fees_cash_str} birr
• Delivery Fees (Points): {delivery_fees_points_str} points
• **Total Cash Revenue: {total_cash_revenue_str} birr**
• **Total Points Used: {total_points_used_str} points**
• Avg Cash Revenue/Day: {avg_cash_revenue_day_str} birr

**{"📈" if company_profit >= 0 else "📉"} Profit/Loss Analytics:**
• Cash Revenue: {cash_revenue_str} birr (50% of cash delivery fees)
• Point Payment Costs: {point_payment_costs_str} birr (50% of point delivery fees)
• **Net Company Profit: {company_profit_str} birr** (Margin: {profit_margin_str}%)
• Personnel Earnings: {personnel_earnings_str} birr (50% of all delivery fees)

**📅 Weekly Breakdown:**
{weekly_breakdown}

**📊 Performance:**
• Completion Rate: {completion_rate_str}%
• Days in Month: {days_in_month}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_monthly"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in monthly analytics: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_transaction_analytics(call):
    """Show comprehensive transaction count analytics with proper order categorization"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading transaction analytics...")

        # Get and validate all order data
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Calculate time periods
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        week_start = today - timedelta(days=today.weekday())
        month_start = today.replace(day=1)

        today_str = today.strftime('%Y-%m-%d')
        yesterday_str = yesterday.strftime('%Y-%m-%d')

        # Get categorized data for different time periods with correct in-progress logic
        # Today's data
        today_completed_orders = {}
        today_confirmed_orders = {}

        # For completed orders: only include orders completed TODAY
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at and completed_at.startswith(today_str):
                today_completed_orders[order_id] = order

        # For confirmed orders: only include orders CREATED TODAY (filter by original creation date)
        # This ensures incomplete orders count respects the time interval filter
        for order_id, order in confirmed_orders_data.items():
            created_at = order.get('created_at', '')
            if created_at and created_at.startswith(today_str):
                today_confirmed_orders[order_id] = order

        today_categorized = categorize_orders_by_status(today_completed_orders, today_confirmed_orders, assignments_data)
        today_stats = calculate_category_percentages(today_categorized)

        # Yesterday's data (for growth calculation)
        yesterday_completed_orders = {}
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at and completed_at.startswith(yesterday_str):
                yesterday_completed_orders[order_id] = order

        yesterday_completed = len(yesterday_completed_orders)

        # Weekly data
        week_completed_orders = {}
        week_confirmed_orders = {}

        # For completed orders: only include orders completed THIS WEEK
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if order_date.date() >= week_start.date():
                        week_completed_orders[order_id] = order
                except:
                    continue

        # For confirmed orders: only include orders CREATED THIS WEEK (filter by original creation date)
        # This ensures incomplete orders count respects the time interval filter
        for order_id, order in confirmed_orders_data.items():
            created_at = order.get('created_at', '')
            if created_at:
                try:
                    order_date = datetime.strptime(created_at[:10], '%Y-%m-%d')
                    if order_date.date() >= week_start.date():
                        week_confirmed_orders[order_id] = order
                except:
                    continue

        week_categorized = categorize_orders_by_status(week_completed_orders, week_confirmed_orders, assignments_data)
        week_stats = calculate_category_percentages(week_categorized)

        # Monthly data
        month_completed_orders = {}
        month_confirmed_orders = {}

        # For completed orders: only include orders completed THIS MONTH
        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if order_date.date() >= month_start.date():
                        month_completed_orders[order_id] = order
                except:
                    continue

        # For confirmed orders: only include orders CREATED THIS MONTH (filter by original creation date)
        # This ensures incomplete orders count respects the time interval filter
        for order_id, order in confirmed_orders_data.items():
            created_at = order.get('created_at', '')
            if created_at:
                try:
                    order_date = datetime.strptime(created_at[:10], '%Y-%m-%d')
                    if order_date.date() >= month_start.date():
                        month_confirmed_orders[order_id] = order
                except:
                    continue

        month_categorized = categorize_orders_by_status(month_completed_orders, month_confirmed_orders, assignments_data)
        month_stats = calculate_category_percentages(month_categorized)

        # All-time data
        alltime_categorized = categorize_orders_by_status(completed_orders_data, confirmed_orders_data, assignments_data)
        alltime_stats = calculate_category_percentages(alltime_categorized)

        # Calculate growth rates (using complete orders only)
        growth_rate = ((today_stats['complete_count'] - yesterday_completed) / yesterday_completed * 100) if yesterday_completed > 0 else 0

        # Calculate averages
        days_in_month = today.day
        weekly_avg = week_stats['total_count'] / 7
        monthly_avg = month_stats['total_count'] / days_in_month

        # Get delivery personnel transaction counts
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        # Count assignments per personnel
        personnel_assignments = {}
        for assignment_id, assignment in assignments_data.items():
            personnel_id = assignment.get('personnel_id', '')
            if personnel_id:
                personnel_assignments[personnel_id] = personnel_assignments.get(personnel_id, 0) + 1

        # Get top performers
        top_performers = []
        for personnel_id, count in sorted(personnel_assignments.items(), key=lambda x: x[1], reverse=True)[:5]:
            person = personnel_data.get(personnel_id, {})
            name = person.get('name', 'Unknown')
            top_performers.append(f"• {name}: {count} deliveries")

        top_performers_str = '\n'.join(top_performers) if top_performers else "• No delivery data available"

        # Format decimal numbers without escaping decimal points
        growth_rate_str = format_number(growth_rate, 1)
        weekly_avg_str = format_number(weekly_avg, 1)
        monthly_avg_str = format_number(monthly_avg, 1)

        text = f"""
🔢 **Transaction Analytics**

**📊 Daily Transactions:**
• Today: {today_stats['total_count']} total ({today_stats['complete_count']} complete, {today_stats['incomplete_count']} in progress, {today_stats['issue_count']} issues)
• Yesterday: {yesterday_completed} completed
• Growth Rate: {growth_rate_str}%

**📅 Period Summaries:**
• This Week: {week_stats['total_count']} total ({week_stats['complete_count']} complete, {week_stats['incomplete_count']} in progress, {week_stats['issue_count']} issues)
• This Month: {month_stats['total_count']} total ({month_stats['complete_count']} complete, {month_stats['incomplete_count']} in progress, {month_stats['issue_count']} issues)
• All Time: {alltime_stats['total_count']} total ({alltime_stats['complete_count']} complete, {alltime_stats['incomplete_count']} in progress, {alltime_stats['issue_count']} issues)

**📈 Averages:**
• Weekly Average: {weekly_avg_str} orders/day
• Monthly Average: {monthly_avg_str} orders/day

**🚚 Top Delivery Personnel:**
{top_performers_str}

**📊 System Status:**
• Active Personnel: {len([p for p in personnel_data.values() if p.get('status') == 'available'])}
• Total Assignments: {len(assignments_data)}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_transactions"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in transaction analytics: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_alltime_analytics(call):
    """Show comprehensive all-time analytics summary with proper order categorization"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading all-time analytics...")

        # Get and validate all order data
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Categorize all orders by status using the same logic as other analytics
        categorized_orders = categorize_orders_by_status(
            completed_orders_data,
            confirmed_orders_data,
            assignments_data
        )

        # Calculate category statistics
        category_stats = calculate_category_percentages(categorized_orders)

        # Calculate all-time metrics with proper categorization
        total_orders = category_stats['total_count']
        complete_orders_count = category_stats['complete_count']
        incomplete_orders_count = category_stats['incomplete_count']
        issue_orders_count = category_stats['issue_count']

        # Calculate all-time revenue analytics using new cost accounting system
        revenue_breakdown = calculate_revenue_breakdown(categorized_orders['complete'])

        total_food_revenue = revenue_breakdown['food_revenue']
        total_cash_revenue = revenue_breakdown['total_cash_revenue']
        total_points_used = revenue_breakdown['total_points_used']
        delivery_fees_cash = revenue_breakdown['delivery_fees_cash']
        delivery_fees_points = revenue_breakdown['delivery_fees_points']
        total_cash_revenue_from_delivery = revenue_breakdown['cash_revenue']
        total_point_payment_costs = revenue_breakdown['point_payment_costs']

        # Calculate net profit using cost accounting (can be negative)
        total_company_profit = revenue_breakdown['company_profit']
        total_personnel_earnings = revenue_breakdown['personnel_earnings']

        total_revenue = total_cash_revenue + total_points_used
        total_profit = total_company_profit
        profit_margin = (total_profit / total_cash_revenue * 100) if total_cash_revenue > 0 else 0

        # Calculate system uptime (days since first order)
        first_order_date = None
        for order in categorized_orders['complete']:
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if first_order_date is None or order_date < first_order_date:
                        first_order_date = order_date
                except:
                    continue

        if first_order_date:
            system_days = (datetime.now() - first_order_date).days + 1
            avg_orders_per_day = complete_orders_count / system_days if system_days > 0 else 0
            avg_revenue_per_day = total_revenue / system_days if system_days > 0 else 0
        else:
            system_days = 0
            avg_orders_per_day = 0
            avg_revenue_per_day = 0

        # Get delivery personnel statistics
        personnel_data = get_data("delivery_personnel") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        total_personnel = len(personnel_data)
        total_assignments = len(assignments_data)

        # Calculate personnel performance
        personnel_stats = {}
        for assignment_id, assignment in assignments_data.items():
            personnel_id = assignment.get('personnel_id', '')
            if personnel_id:
                if personnel_id not in personnel_stats:
                    personnel_stats[personnel_id] = {'assignments': 0, 'completed': 0}
                personnel_stats[personnel_id]['assignments'] += 1
                if assignment.get('status') == 'delivered':
                    personnel_stats[personnel_id]['completed'] += 1

        avg_assignments_per_personnel = total_assignments / total_personnel if total_personnel > 0 else 0

        # Escape decimal numbers
        total_food_revenue_str = escape_markdown(f"{total_food_revenue:.2f}")
        total_cash_revenue_str = escape_markdown(f"{total_cash_revenue:.2f}")
        delivery_fees_cash_str = escape_markdown(f"{delivery_fees_cash:.2f}")
        delivery_fees_points_str = escape_markdown(f"{delivery_fees_points:.0f}")
        total_points_used_str = escape_markdown(f"{total_points_used:.0f}")
        total_cash_revenue_from_delivery_str = escape_markdown(f"{total_cash_revenue_from_delivery:.2f}")
        total_point_payment_costs_str = escape_markdown(f"{total_point_payment_costs:.2f}")
        total_profit_str = escape_markdown(f"{total_profit:.2f}")
        profit_margin_str = escape_markdown(f"{profit_margin:.1f}")
        total_personnel_earnings_str = escape_markdown(f"{total_personnel_earnings:.2f}")
        total_company_profit_str = format_number(total_company_profit, 2)
        avg_orders_per_day_str = format_number(avg_orders_per_day, 1)
        avg_cash_revenue_per_day_str = format_number((total_cash_revenue/system_days) if system_days > 0 else 0, 2)
        avg_assignments_per_personnel_str = format_number(avg_assignments_per_personnel, 1)
        avg_order_value_str = format_number((total_cash_revenue/complete_orders_count) if complete_orders_count > 0 else 0, 2)

        text = f"""
🌟 **All-Time Analytics**
**System Active Since:** {first_order_date.strftime('%Y-%m-%d') if first_order_date else 'N/A'}

**📊 Order Status Summary:** {total_orders} total orders
• ✅ Complete: {complete_orders_count} ({category_stats['complete_percentage']:.1f}%)
• 🔄 In Progress: {incomplete_orders_count} ({category_stats['incomplete_percentage']:.1f}%)
• ⚠️ Issues: {issue_orders_count} ({category_stats['issue_percentage']:.1f}%)
• Success Rate: {escape_markdown(f"{(complete_orders_count/total_orders*100) if total_orders > 0 else 0:.1f}")}%

**💰 Revenue Analytics:**
• Food Revenue: {total_food_revenue_str} birr (cash)
• Delivery Fees (Cash): {delivery_fees_cash_str} birr
• Delivery Fees (Points): {delivery_fees_points_str} points
• **Total Cash Revenue: {total_cash_revenue_str} birr**
• **Total Points Used: {total_points_used_str} points**
• Average Order Value: {avg_order_value_str} birr (cash)

**{"📈" if total_company_profit >= 0 else "📉"} Profit/Loss Analytics:**
• Cash Revenue: {total_cash_revenue_from_delivery_str} birr (50% of cash delivery fees)
• Point Payment Costs: {total_point_payment_costs_str} birr (50% of point delivery fees)
• **Net Company Profit: {total_company_profit_str} birr** (Margin: {profit_margin_str}%)
• Personnel Earnings: {total_personnel_earnings_str} birr (50% of all delivery fees)

**📅 Performance Metrics:**
• System Days: {system_days}
• Avg Orders/Day: {avg_orders_per_day_str}
• Avg Cash Revenue/Day: {avg_cash_revenue_per_day_str} birr

**👥 Personnel Statistics:**
• Total Personnel: {total_personnel}
• Total Assignments: {total_assignments}
• Avg Assignments/Personnel: {avg_assignments_per_personnel_str}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_alltime"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        logger.error(f"Error in all-time analytics: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_delivery_analytics(call):
    """Show delivery performance analytics"""
    try:
        # Get delivery personnel performance data
        personnel_data = get_data("delivery_personnel") or {}
        performance_data = get_data("delivery_personnel_performance") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        if not personnel_data:
            text = "❌ **No Delivery Data**\n\nNo delivery personnel found in the system."
        else:
            text = "🚚 **Delivery Analytics**\n\n"

            # Overall stats
            total_personnel = len(personnel_data)
            active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])
            busy_personnel = len([p for p in personnel_data.values() if p.get('status') == 'busy'])

            text += f"""**Personnel Overview:**
• Total Personnel: {total_personnel}
• Currently Active: {active_personnel}
• Currently Busy: {busy_personnel}
• Offline: {total_personnel - active_personnel - busy_personnel}

**Top Performers:**
            """

            # Get top performers (simplified)
            performer_stats = []
            for personnel_id, person in personnel_data.items():
                name = person.get('name', 'Unknown')
                perf = performance_data.get(personnel_id, {})
                completed = perf.get('completed_deliveries', 0)
                performer_stats.append((name, completed))

            # Sort by completed deliveries
            performer_stats.sort(key=lambda x: x[1], reverse=True)

            for i, (name, completed) in enumerate(performer_stats[:5], 1):
                text += f"\n{i}. {name}: {completed} deliveries"

            # Current assignments
            active_assignments = len([a for a in assignments_data.values() if a.get('status') == 'active'])
            text += f"\n\n**Current Status:**\n• Active Assignments: {active_assignments}"

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_delivery"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

    except Exception as e:
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_payroll_analytics(call):
    """Show optimized delivery personnel payroll system with real-time data"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading payroll system...")

        # Force refresh analytics data for real-time payroll calculations
        analytics_data = refresh_analytics_data()

        # Get and validate data from refreshed collections
        personnel_data = validate_analytics_data(analytics_data.get('personnel', {}), "personnel")
        assignments_data = validate_analytics_data(analytics_data.get('assignments', {}), "assignments")
        completed_orders_data = validate_analytics_data(analytics_data.get('completed_orders', {}), "completed_orders")

        if not personnel_data:
            text = "❌ **No Personnel Data**\n\nNo delivery personnel found in the system."
        else:
            # Calculate payroll for each personnel
            payroll_data = []
            total_personnel_earnings = 0
            total_completed_deliveries = 0

            for personnel_id, person in personnel_data.items():
                name = person.get('name', 'Unknown')

                # Count completed deliveries for this personnel
                completed_deliveries = 0
                total_delivery_fees = 0

                for assignment_id, assignment in assignments_data.items():
                    if assignment.get('personnel_id') == personnel_id and assignment.get('status') == 'delivered':
                        order_number = assignment.get('order_number', '')
                        # Find the corresponding completed order
                        for order_id, order in completed_orders_data.items():
                            if order.get('order_number') == order_number:
                                completed_deliveries += 1
                                delivery_fee = safe_get_numeric_value(order, 'delivery_fee', 0)
                                total_delivery_fees += delivery_fee
                                break

                # Calculate earnings (50% of delivery fees from completed orders)
                personnel_earnings = total_delivery_fees * 0.5
                total_personnel_earnings += personnel_earnings
                total_completed_deliveries += completed_deliveries

                if personnel_earnings > 0:  # Only include personnel with earnings
                    payroll_data.append({
                        'name': name,
                        'completed_deliveries': completed_deliveries,
                        'earnings': personnel_earnings
                    })

            # Sort by earnings (highest first) and limit to top 5
            payroll_data.sort(key=lambda x: x['earnings'], reverse=True)
            top_earners = payroll_data[:5]

            # Create optimized payroll list
            payroll_list, total_earners = optimize_personnel_list(
                [{'name': p['name'], 'earnings': p['earnings'], 'status': 'active'} for p in top_earners],
                max_items=5
            )

            # Calculate summary statistics
            avg_earnings = total_personnel_earnings / len(personnel_data) if len(personnel_data) > 0 else 0
            active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])

            text = f"""💰 **Payroll System**

📊 **Summary:** {len(personnel_data)} staff | {total_completed_deliveries} deliveries
💵 **Total Payout:** {total_personnel_earnings:.0f} birr
📈 **Avg Earnings:** {avg_earnings:.0f} birr/person

🏆 **Top Earners:**
{payroll_list}

📋 **Payment:** 50% of delivery fees from completed orders
🔄 **Active Staff:** {active_personnel} available"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_payroll"),
            types.InlineKeyboardButton("📊 All Staff", callback_data="payroll_details")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe edit message function
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        handle_analytics_error(call, str(e), "payroll_analytics")

def show_trend_analytics(call):
    """Show trend analysis"""
    management_bot.answer_callback_query(call.id, "💹 Trend analysis coming soon!")

# Detail View Functions
def show_daily_details(call):
    """Show detailed daily analytics with full breakdown"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading detailed daily analytics...")

        today = datetime.now().strftime("%Y-%m-%d")

        # Get and validate data
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        confirmed_orders_data = validate_analytics_data(get_data("confirmed_orders"), "confirmed_orders")
        personnel_data = validate_analytics_data(get_data("delivery_personnel"), "personnel")
        assignments_data = validate_analytics_data(get_data("delivery_personnel_assignments"), "assignments")

        # Filter today's orders and convert to dictionaries for categorization
        today_completed_orders = {}
        today_confirmed_orders = {}

        for order_id, order in completed_orders_data.items():
            completed_at = order.get('completed_at', '')
            if completed_at and completed_at.startswith(today):
                today_completed_orders[order_id] = order

        for order_id, order in confirmed_orders_data.items():
            created_at = order.get('created_at', '')
            if created_at and created_at.startswith(today):
                today_confirmed_orders[order_id] = order

        # Categorize today's orders by status
        categorized_orders = categorize_orders_by_status(
            today_completed_orders,
            today_confirmed_orders,
            assignments_data
        )

        # Calculate category statistics
        category_stats = calculate_category_percentages(categorized_orders)

        # Calculate detailed metrics
        total_orders = category_stats['total_count']
        food_revenue = sum(safe_get_numeric_value(order, 'subtotal', 0) for order in categorized_orders['complete'])
        delivery_revenue = sum(safe_get_numeric_value(order, 'delivery_fee', 0) for order in categorized_orders['complete'])
        total_revenue = food_revenue + delivery_revenue

        # Personnel breakdown
        personnel_activity = {}
        for assignment_id, assignment in assignments_data.items():
            if assignment.get('status') == 'delivered':
                order_number = assignment.get('order_number', '')
                # Check if this order was completed today
                for order in today_completed_orders.values():
                    if order.get('order_number') == order_number:
                        personnel_id = assignment.get('personnel_id', '')
                        if personnel_id in personnel_data:
                            name = personnel_data[personnel_id].get('name', 'Unknown')
                            if name not in personnel_activity:
                                personnel_activity[name] = 0
                            personnel_activity[name] += 1
                        break

        # Create detailed personnel list (top 5)
        top_personnel = sorted(personnel_activity.items(), key=lambda x: x[1], reverse=True)[:5]
        personnel_breakdown = '\n'.join([f"• {name}: {count} deliveries" for name, count in top_personnel])

        if not personnel_breakdown:
            personnel_breakdown = "• No deliveries completed today"

        # Hourly breakdown (simplified)
        hourly_orders = {}
        for order in today_completed_orders.values():
            completed_at = order.get('completed_at', '')
            if completed_at and len(completed_at) >= 13:
                hour = completed_at[11:13]
                hourly_orders[hour] = hourly_orders.get(hour, 0) + 1

        # Show top 3 hours
        top_hours = sorted(hourly_orders.items(), key=lambda x: x[1], reverse=True)[:3]
        hourly_breakdown = ' | '.join([f"{hour}:00 ({count})" for hour, count in top_hours])

        if not hourly_breakdown:
            hourly_breakdown = "No completed orders yet"

        text = f"""📅 **Daily Details - {today}**

📊 **Order Status Breakdown:** {total_orders} total orders
• ✅ Complete: {category_stats['complete_count']} ({category_stats['complete_percentage']:.1f}%)
• 🔄 In Progress: {category_stats['incomplete_count']} ({category_stats['incomplete_percentage']:.1f}%)
• ⚠️ Issues: {category_stats['issue_count']} ({category_stats['issue_percentage']:.1f}%)

💰 **Revenue Breakdown:**
• Food Sales: {food_revenue:.0f} birr
• Delivery Fees: {delivery_revenue:.0f} birr
• Total Revenue: {total_revenue:.0f} birr
• Avg Order Value: {(total_revenue/category_stats['complete_count']) if category_stats['complete_count'] > 0 else 0:.0f} birr

👥 **Top Performers Today:**
{personnel_breakdown}

🕐 **Peak Hours:** {hourly_breakdown}

📈 **Profit Split:**
• Personnel Earnings: {delivery_revenue * 0.5:.0f} birr
• Company Profit: {delivery_revenue * 0.5:.0f} birr"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 Summary", callback_data="analytics_daily"),
            types.InlineKeyboardButton("🔄 Refresh", callback_data="daily_details")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe edit message function
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        handle_analytics_error(call, str(e), "daily_details")

def show_weekly_details(call):
    """Show detailed weekly analytics with full breakdown"""
    try:
        management_bot.answer_callback_query(call.id, "📊 Loading detailed weekly analytics...")

        # Calculate week range
        today = datetime.now()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)

        # Get and validate data
        completed_orders_data = validate_analytics_data(get_data("completed_orders"), "completed_orders")
        personnel_data = validate_analytics_data(get_data("delivery_personnel"), "personnel")

        # Filter week's completed orders
        week_completed_orders = []
        for order in completed_orders_data.values():
            completed_at = order.get('completed_at', '')
            if completed_at:
                try:
                    order_date = datetime.strptime(completed_at[:10], '%Y-%m-%d')
                    if week_start.date() <= order_date.date() <= week_end.date():
                        week_completed_orders.append(order)
                except:
                    continue

        # Calculate metrics
        total_revenue = sum(
            safe_get_numeric_value(order, 'subtotal', 0) + safe_get_numeric_value(order, 'delivery_fee', 0)
            for order in week_completed_orders
        )

        # Daily breakdown (all 7 days)
        daily_breakdown = []
        for i in range(7):
            day = week_start + timedelta(days=i)
            day_str = day.strftime('%Y-%m-%d')
            day_name = day.strftime('%a')

            day_orders = [o for o in week_completed_orders if o.get('completed_at', '').startswith(day_str)]
            day_revenue = sum(
                safe_get_numeric_value(o, 'subtotal', 0) + safe_get_numeric_value(o, 'delivery_fee', 0)
                for o in day_orders
            )

            daily_breakdown.append(f"• {day_name}: {len(day_orders)} orders ({day_revenue:.0f} birr)")

        daily_text = '\n'.join(daily_breakdown)

        text = f"""📊 **Weekly Details**
**Period:** {week_start.strftime('%m/%d')} - {week_end.strftime('%m/%d')}

📈 **Summary:**
• Total Orders: {len(week_completed_orders)}
• Total Revenue: {total_revenue:.0f} birr
• Daily Average: {len(week_completed_orders)/7:.1f} orders

📅 **Daily Breakdown:**
{daily_text}

📊 **Performance:**
• Best Day: {max(daily_breakdown, key=lambda x: int(x.split()[1])) if daily_breakdown else 'N/A'}
• Revenue/Day: {total_revenue/7:.0f} birr avg"""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 Summary", callback_data="analytics_weekly"),
            types.InlineKeyboardButton("🔄 Refresh", callback_data="weekly_details")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        # Use safe edit message function
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        handle_analytics_error(call, str(e), "weekly_details")

def show_more_analytics_details(call):
    """Show additional analytics details when content was truncated"""
    try:
        management_bot.answer_callback_query(call.id, "📄 Loading additional details...")

        text = """📄 **Additional Analytics Details**

This feature shows extended analytics data when the main view is truncated due to length limits.

**Available Detail Views:**
• Daily Details - Hourly breakdown, top performers
• Weekly Details - Day-by-day analysis
• Personnel Reports - Individual performance
• Revenue Breakdown - Detailed financial analysis

**Navigation:**
Use the specific detail buttons in each analytics view to access comprehensive data.

**Note:** Detail views are optimized for mobile display while maintaining all essential information."""

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("📊 Analytics Menu", callback_data="mgmt_analytics"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_main")
        )

        # Use safe edit message function
        if not safe_edit_message(call, text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)

    except Exception as e:
        handle_analytics_error(call, str(e), "more_details")

def handle_personnel_update(call):
    """Stub for personnel update callback - not yet implemented"""
    management_bot.answer_callback_query(call.id, "⚠️ Personnel update not implemented yet.")

def register_management_bot_handlers():
    """Register all management bot handlers"""
    try:
        # Register start and help command handler
        management_bot.register_message_handler(handle_start, commands=['start', 'help'])

        # Register callback query handler
        management_bot.register_callback_query_handler(handle_callback_query, func=lambda call: True)

        # Note: Removed global text message handler to prevent conflicts with next_step_handlers
        # Individual functions now use register_next_step_handler for specific text input

    except Exception as e:
        raise

def run_management_bot():
    """Run the management bot"""
    try:
        # Register handlers first
        register_management_bot_handlers()

        # Start polling
        management_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        raise

# Edit Personnel Functions
def start_edit_name(call):
    """Start editing personnel name"""
    personnel_id = call.data.replace("edit_name_", "")
    management_bot.answer_callback_query(call.id, "📝 Edit name...")

    # Get current name
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(call.id, "❌ Personnel not found!", show_alert=True)
        return

    current_name = person.get('name', 'Unknown')

    text = f"""
📝 **Edit Name**

**Current Name:** {current_name}

Please reply with the new name for this personnel:
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Store edit context and set up handler
    management_bot.register_next_step_handler(
        call.message,
        lambda msg: process_name_edit(msg, personnel_id)
    )

def start_edit_phone(call):
    """Start editing personnel phone"""
    personnel_id = call.data.replace("edit_phone_", "")
    management_bot.answer_callback_query(call.id, "📞 Edit phone...")

    # Get current phone
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(call.id, "❌ Personnel not found!", show_alert=True)
        return

    current_phone = person.get('phone_number', 'N/A')

    text = f"""
📞 **Edit Phone Number**

**Current Phone:** {current_phone}

Please reply with the new phone number (include country code):
**Example:** +251912345678
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Store edit context and set up handler
    management_bot.register_next_step_handler(
        call.message,
        lambda msg: process_phone_edit(msg, personnel_id)
    )

def start_edit_telegram_id(call):
    """Start editing personnel Telegram ID"""
    personnel_id = call.data.replace("edit_telegram_", "")
    management_bot.answer_callback_query(call.id, "📱 Edit Telegram ID...")

    # Get current Telegram ID
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(call.id, "❌ Personnel not found!", show_alert=True)
        return

    current_telegram_id = person.get('telegram_id', 'N/A')

    text = f"""
📱 **Edit Telegram ID**

**Current Telegram ID:** {current_telegram_id}

Please reply with the new Telegram ID (numbers only):
**Example:** 123456789
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Store edit context and set up handler
    management_bot.register_next_step_handler(
        call.message,
        lambda msg: process_telegram_id_edit(msg, personnel_id)
    )

def process_name_edit(message, personnel_id):
    """Process name edit"""
    try:
        new_name = message.text.strip()

        if not validate_name(new_name):
            management_bot.reply_to(
                message,
                "❌ **Invalid Name**\n\n"
                "Name must be 2-50 characters and contain letters, numbers, spaces, and common punctuation (hyphens, apostrophes, periods, commas).\n\n"
                "✅ **Valid examples:** John O'Connor, Mary-Jane, Ahmed Al-Hassan, Driver-01, Dr. Smith",
                parse_mode='Markdown'
            )
            return

        # Update personnel data
        personnel_data = get_data("delivery_personnel") or {}
        if personnel_id not in personnel_data:
            management_bot.reply_to(
                message,
                "❌ **Personnel Not Found**\n\nPersonnel record not found in database.",
                parse_mode='Markdown'
            )
            return

        old_name = personnel_data[personnel_id].get('name', 'Unknown')
        personnel_data[personnel_id]['name'] = new_name

        # Save to Firebase with enhanced data integrity
        success = safe_firebase_set("delivery_personnel", personnel_data)

        # Verify the update was successful
        if success:
            if not verify_personnel_data_integrity(personnel_id, personnel_data[personnel_id]):
                logger.error(f"Data integrity verification failed after name update for {personnel_id}")
                notify_admin_error(f"Name update integrity verification failed for {personnel_id}")
                success = False

        if success:
            # Create success message with back button
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

            management_bot.reply_to(
                message,
                f"✅ **Name Updated Successfully!**\n\n"
                f"**Old Name:** {old_name}\n"
                f"**New Name:** {new_name}\n\n"
                f"All earnings data and performance metrics have been preserved.",
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
        else:
            management_bot.reply_to(
                message,
                "❌ **Update Failed**\n\nFailed to save changes to database. Please try again.",
                parse_mode='Markdown'
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error**\n\nAn error occurred: {str(e)}",
            parse_mode='Markdown'
        )

def process_phone_edit(message, personnel_id):
    """Process phone edit"""
    try:
        new_phone = message.text.strip()

        # Validate phone using validation function
        if not validate_phone_number(new_phone):
            management_bot.reply_to(
                message,
                "❌ **Invalid Phone Number**\n\nPhone number should start with '+' (international) or '0' (local) and be at least 10 characters long.",
                parse_mode='Markdown'
            )
            return

        # Update personnel data
        personnel_data = get_data("delivery_personnel") or {}
        if personnel_id not in personnel_data:
            management_bot.reply_to(
                message,
                "❌ **Personnel Not Found**\n\nPersonnel record not found in database.",
                parse_mode='Markdown'
            )
            return

        old_phone = personnel_data[personnel_id].get('phone_number', 'N/A')
        personnel_data[personnel_id]['phone_number'] = new_phone

        # Save to Firebase with enhanced data integrity
        success = safe_firebase_set("delivery_personnel", personnel_data)

        # Verify the update was successful
        if success:
            if not verify_personnel_data_integrity(personnel_id, personnel_data[personnel_id]):
                logger.error(f"Data integrity verification failed after phone update for {personnel_id}")
                notify_admin_error(f"Phone update integrity verification failed for {personnel_id}")
                success = False

        if success:
            # Create success message with back button
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

            management_bot.reply_to(
                message,
                f"✅ **Phone Updated Successfully!**\n\n"
                f"**Old Phone:** {old_phone}\n"
                f"**New Phone:** {new_phone}\n\n"
                f"All earnings data and performance metrics have been preserved.",
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
        else:
            management_bot.reply_to(
                message,
                "❌ **Update Failed**\n\nFailed to save changes to database. Please try again.",
                parse_mode='Markdown'
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error**\n\nAn error occurred: {str(e)}",
            parse_mode='Markdown'
        )

def process_telegram_id_edit(message, personnel_id):
    """Process Telegram ID edit"""
    try:
        new_telegram_id = message.text.strip()

        # Validate Telegram ID using comprehensive validation
        validation_result = None
        try:
            # Convert to int and check if it's a valid Telegram ID
            tid = int(new_telegram_id)
            # Telegram IDs are typically 9-10 digits
            if 100000000 <= tid <= 9999999999:
                validation_result = (True, tid)
            else:
                validation_result = (False, None)
        except (ValueError, TypeError):
            validation_result = (False, None)

        is_valid, validated_telegram_id = validation_result
        if not is_valid:
            management_bot.reply_to(
                message,
                f"❌ **Invalid Telegram ID**\n\nTelegram ID must be numeric and 9-10 digits. You provided: {new_telegram_id}",
                parse_mode='Markdown'
            )
            return

        # Check if Telegram ID already exists
        personnel_data = get_data("delivery_personnel") or {}
        for pid, pdata in personnel_data.items():
            if pid != personnel_id:
                # Handle both string and integer comparisons for backward compatibility
                existing_tid = pdata.get('telegram_id')
                if existing_tid and (str(existing_tid) == str(validated_telegram_id) or
                                    (isinstance(existing_tid, str) and existing_tid.isdigit() and int(existing_tid) == validated_telegram_id) or
                                    existing_tid == validated_telegram_id):
                    management_bot.reply_to(
                        message,
                        f"❌ **Telegram ID Already Exists**\n\n"
                        f"A personnel with Telegram ID {validated_telegram_id} already exists.\n\n"
                        f"Personnel: {pdata.get('name', 'Unknown')} (ID: {pid})",
                        parse_mode='Markdown'
                    )
                    return

        if personnel_id not in personnel_data:
            management_bot.reply_to(
                message,
                "❌ **Personnel Not Found**\n\nPersonnel record not found in database.",
                parse_mode='Markdown'
            )
            return

        old_telegram_id = personnel_data[personnel_id].get('telegram_id', 'N/A')
        personnel_data[personnel_id]['telegram_id'] = new_telegram_id

        # Save to Firebase with enhanced data integrity
        success = safe_firebase_set("delivery_personnel", personnel_data)

        # Verify the update was successful
        if success:
            if not verify_personnel_data_integrity(personnel_id, personnel_data[personnel_id]):
                logger.error(f"Data integrity verification failed after Telegram ID update for {personnel_id}")
                notify_admin_error(f"Telegram ID update integrity verification failed for {personnel_id}")
                success = False

        if success:
            # Create success message with back button
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

            management_bot.reply_to(
                message,
                f"✅ **Telegram ID Updated Successfully!**\n\n"
                f"**Old Telegram ID:** {old_telegram_id}\n"
                f"**New Telegram ID:** {new_telegram_id}\n\n"
                f"All earnings data and performance metrics have been preserved.",
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
        else:
            management_bot.reply_to(
                message,
                "❌ **Update Failed**\n\nFailed to save changes to database. Please try again.",
                parse_mode='Markdown'
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error**\n\nAn error occurred: {str(e)}",
            parse_mode='Markdown'
        )

def show_weekly_earnings_report(call):
    """Show comprehensive weekly earnings report"""
    management_bot.answer_callback_query(call.id, "📊 Generating weekly report...")

    try:
        # Get weekly earnings report
        report = get_weekly_earnings_report()

        if not report or report['personnel_count'] == 0:
            text = """
📊 **Weekly Earnings Report**

❌ **No Data Available**

There are no delivery personnel with earnings data for this week.

**Possible Reasons:**
• No personnel registered in system
• No deliveries completed this week
• Earnings tracking not initialized
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )
        else:
            # Format the report with escaped decimal numbers
            total_weekly_str = escape_markdown(f"{report['total_weekly_earnings']:.2f}")
            avg_earnings_str = escape_markdown(f"{report['average_earnings_per_person']:.2f}")
            avg_deliveries_str = escape_markdown(f"{report['average_deliveries_per_person']:.1f}")

            text = f"""
📊 **Weekly Earnings Report**
**Period:** {report['week_start']} to {report['week_end']}

**📈 Summary:**
• Total Personnel: {report['personnel_count']}
• Total Weekly Earnings: {total_weekly_str} birr
• Total Weekly Deliveries: {report['total_weekly_deliveries']}
• Average Earnings/Person: {avg_earnings_str} birr
• Average Deliveries/Person: {avg_deliveries_str}

**👥 Personnel Performance:**
            """

            # Add individual personnel reports with escaped decimals
            for i, person_report in enumerate(report['personnel_reports'][:10], 1):  # Top 10
                weekly_earnings_str = escape_markdown(f"{person_report['weekly_earnings']:.2f}")
                daily_earnings_str = escape_markdown(f"{person_report['daily_earnings']:.2f}")
                text += f"""
{i}. **{person_report['name']}**
   • Weekly: {weekly_earnings_str} birr ({person_report['weekly_deliveries']} deliveries)
   • Today: {daily_earnings_str} birr ({person_report['daily_deliveries']} deliveries)
                """

            if len(report['personnel_reports']) > 10:
                text += f"\n... and {len(report['personnel_reports']) - 10} more personnel"

            keyboard = types.InlineKeyboardMarkup(row_width=2)
            keyboard.add(
                types.InlineKeyboardButton("🔄 Refresh Report", callback_data="pers_weekly_report"),
                types.InlineKeyboardButton("📊 Reset Weekly", callback_data="reset_weekly_earnings")
            )
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error generating weekly earnings report: {e}")
        management_bot.edit_message_text(
            f"❌ **Error Generating Report**\n\nAn error occurred while generating the weekly earnings report: {str(e)}",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

def confirm_weekly_reset(call):
    """Confirm weekly earnings reset"""
    management_bot.answer_callback_query(call.id, "⚠️ Preparing weekly reset...")

    text = """
⚠️ **Confirm Weekly Earnings Reset**

**WARNING:** This action will reset weekly earnings for ALL delivery personnel to zero.

**What will be reset:**
• Weekly earnings (set to 0.00 birr)
• Weekly delivery counts (set to 0)
• Weekly start date (set to current Monday)

**What will NOT be affected:**
• Daily earnings (preserved)
• Lifetime total earnings (preserved)
• Personnel records (preserved)
• Performance metrics (preserved)

**This action cannot be undone!**

Are you sure you want to proceed?
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✅ Yes, Reset", callback_data="execute_weekly_reset"),
        types.InlineKeyboardButton("❌ Cancel", callback_data="pers_weekly_report")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def execute_weekly_reset(call):
    """Execute weekly earnings reset for all personnel"""
    management_bot.answer_callback_query(call.id, "🔄 Resetting weekly earnings...")

    try:
        from src.utils.earnings_utils import reset_weekly_earnings_for_all

        success = reset_weekly_earnings_for_all()

        if success:
            text = """
✅ **Weekly Earnings Reset Completed**

**Successfully reset weekly earnings for all delivery personnel.**

**What was reset:**
• Weekly earnings → 0.00 birr
• Weekly delivery counts → 0
• Weekly start date → Current Monday

**What was preserved:**
• Daily earnings
• Lifetime total earnings
• Personnel records
• Performance metrics

All personnel can now start earning for the new week.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("📊 View Updated Report", callback_data="pers_weekly_report")
            )
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )
        else:
            text = """
❌ **Weekly Reset Failed**

An error occurred while resetting weekly earnings. Some personnel may not have been reset.

**Recommended Actions:**
• Check system logs for details
• Try the reset operation again
• Contact system administrator if problem persists
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔄 Try Again", callback_data="reset_weekly_earnings")
            )
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error executing weekly reset: {e}")
        management_bot.edit_message_text(
            f"❌ **Reset Error**\n\nAn error occurred during the weekly reset: {str(e)}",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

def backup_delivery_personnel():
    # Export all delivery personnel data from Firestore to a local JSON file
    try:
        from src.firebase_db import get_data
        personnel = get_data('delivery_personnel')
        with open('delivery_personnel_backup.json', 'w', encoding='utf-8') as f:
            json.dump(personnel, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"Failed to backup delivery personnel: {e}")
        notify_admin_error(f"Failed to backup delivery personnel: {e}")
        return False

@management_bot.message_handler(commands=['backup_personnel'])
def handle_backup_personnel(message):
    user_id = message.from_user.id
    if not is_admin(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    if is_rate_limited(user_id):
        management_bot.reply_to(message, "⏳ Too many actions. Please wait a minute and try again.")
        return
    if backup_delivery_personnel():
        management_bot.reply_to(message, "✅ Delivery personnel data backed up to delivery_personnel_backup.json")
    else:
        management_bot.reply_to(message, "❗️ Failed to backup delivery personnel data. The admin has been notified.")

if __name__ == "__main__":
    run_management_bot()
